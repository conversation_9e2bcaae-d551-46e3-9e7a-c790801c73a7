<template>
  <div class="container">
    <nav class="fixed-nav">
      <el-form :inline="true">
        <el-form-item label="知识库名称：">
          <el-input
            v-model="searchQuery"
            placeholder="搜索知识库"
            @keyup.enter="search"></el-input>
        </el-form-item>

        <el-form-item>
          <div
            class="btns"
            @click="search">
            搜索
          </div>
        </el-form-item>
      </el-form>
      <div>
        <!-- <div
          class="btns"
          @click="openLineageDialog">
          知识库谱系化
        </div> -->
        <div
          class="btns"
          @click="showAddDialog">
          新建知识库
        </div>
        <div
          class="btns"
          @click="router.go(-1)">
          返回
        </div>
      </div>
    </nav>

    <main class="scrollable-content">
      <div
        class="items"
        v-for="item in filteredKnowledgeBases"
        :key="item.kb_name">
        <div class="kb-header">
          <p class="kb-name">{{ item.kb_name }}</p>
          <p class="kb-date">{{ formatDate(item.updated_at || item.created_at) }}</p>
        </div>

        <div class="kb-description">
          {{ item.description || "暂无描述" }}
        </div>

        <div class="kb-files">
          <img src="/assets/images/文档.png" />
          {{ item.file_count || 0 }}个文件
        </div>

        <div class="kb-actions">
          <el-button
            @click="editKnowledge(item)"
            link
            type="primary">
            <el-icon><Edit /></el-icon>编辑
          </el-button>
          <el-button
            @click="showDocumentDialog(item)"
            link
            type="primary">
            <el-icon><Document /></el-icon>文档处理
          </el-button>
          <el-button
            @click="deleteKnowledge(item)"
            link
            type="danger">
            <el-icon><Delete /></el-icon>删除
          </el-button>
        </div>
      </div>
    </main>

    <!-- 文件处理组件 -->
    <FileProcess :onProcessComplete="handleProcessComplete" />

    <!-- 处理进度对话框 -->
    <el-dialog
      v-model="processing"
      title="处理进度"
      width="400px">
      <div class="progress-info">
        <el-progress
          :percentage="processingStatus.progress"
          :status="processingStatus.progress >= 100 ? 'success' : ''" />
        <p>{{ processingStatus.message }}</p>
      </div>
    </el-dialog>

    <!-- Include the DataBaseGraph component -->
    <DataBaseGraph />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from "vue";
  import { useRouter } from "vue-router";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  import FileProcess from "./FileProcess.vue";
  // Import the DataBaseGraph component
  import DataBaseGraph from "./DataBaseGraph.vue";

  const router = useRouter();
  const dialogStore = useDialogStore();
  const { addKnowledgeShow, addKnowledgeType, editKnowledgeForm, fileProcessShow, lineageDialogShow } =
    storeToRefs(dialogStore);

  // 状态定义
  const searchQuery = ref("");
  const knowledgeBases = ref([]);
  const currentKnowledgeBase = ref(null);
  const processing = ref(false);
  const processingStatus = ref({
    processing: false,
    progress: 0,
    status: "",
    message: "",
  });

  // 计算属性
  const filteredKnowledgeBases = computed(() => {
    return knowledgeBases.value
      .filter(
        (item) =>
          item.kb_name
            .toLowerCase()
            .includes(searchQuery.value.toLowerCase()) ||
          (item.description &&
            item.description
              .toLowerCase()
              .includes(searchQuery.value.toLowerCase()))
      )
      .sort((a, b) => {
        const timeA = new Date(a.updated_at || a.created_at);
        const timeB = new Date(b.updated_at || b.created_at);
        return timeB - timeA;
      });
  });

  // 基础方法
  const formatDate = (date) => {
    if (!date) return "";
    const d = new Date(date);
    if (isNaN(d.getTime())) return "";
    return d.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 知识库操作方法
  const showAddDialog = () => {
    editKnowledgeForm.value = {
      kb_name: "",
      description: "",
    };
    addKnowledgeType.value = "add";
    addKnowledgeShow.value = true;
  };

  const editKnowledge = (item) => {
    editKnowledgeForm.value = {
      kb_id: item.kb_id, // 修复：确保包含 kb_id
      kb_name: item.kb_name,
      description: item.description,
    };
    addKnowledgeType.value = "edit";
    addKnowledgeShow.value = true;
  };

  const deleteKnowledge = async (item) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除知识库 "${item.kb_name}" 吗？`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );

      const response = await fetch(
        `/dev-api/delete_knowledge_base/${item.kb_id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      if (data.code !== 0) {
        throw new Error(data.msg || "删除失败");
      }

      ElMessage.success(data.msg || "删除成功");
      await fetchKnowledgeBases();
    } catch (error) {
      if (error === 'cancel' || error.toString().includes('cancel')) {
        return;
      }
      ElMessage.error("删除知识库失败：" + error.message);
    }
  };

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    try {
      // 使用新的API一次性获取所有知识库的详细信息
      const response = await fetch("/dev-api/get_all_knowledge_bases");
      const result = await response.json();
      
      if (result.code !== 0) {
        throw new Error(result.msg || "获取知识库列表失败");
      }

      // 直接使用返回的数据，不再需要为每个知识库单独请求详情
      knowledgeBases.value = result.data.map(kb => ({
        kb_id: kb.id,           // 添加 kb_id 字段
        kb_name: kb.name,
        description: kb.description || "",
        created_at: kb.created_at,
        updated_at: kb.updated_at,
        file_count: kb.file_count || 0,
        files: [] // 文件列表在需要时再加载
      }));
    } catch (error) {
      console.error("获取知识库列表失败:", error);
      ElMessage.error("获取知识库列表失败：" + error.message);
      knowledgeBases.value = [];
    }
  };

  // 搜索方法
  const search = async () => {
    if (!searchQuery.value.trim()) {
      await fetchKnowledgeBases();
      return;
    }

    // 先获取所有知识库，然后在前端过滤
    try {
      const response = await fetch("/dev-api/get_all_knowledge_bases");
      const result = await response.json();
      
      if (result.code !== 1) {
        throw new Error(result.msg || "获取知识库列表失败");
      }

      // 在前端进行过滤
      const searchTerm = searchQuery.value.toLowerCase().trim();
      const filteredResults = result.data.filter(kb => 
        kb.name.toLowerCase().includes(searchTerm) || 
        (kb.description && kb.description.toLowerCase().includes(searchTerm))
      );

      if (filteredResults.length === 0) {
        ElMessage.warning("未找到匹配的知识库");
        return;
      }

      // 转换为前端所需格式
      knowledgeBases.value = filteredResults.map(kb => ({
        kb_id: kb.id,           // 添加 kb_id 字段
        kb_name: kb.name,
        description: kb.description || "",
        created_at: kb.created_at,
        updated_at: kb.updated_at,
        file_count: kb.file_count || 0,
        files: []
      }));
    } catch (error) {
      console.error("搜索知识库失败:", error);
      ElMessage.warning("搜索知识库时发生错误：" + error.message);
    }
  };

  // 显示文档处理对话框
  const showDocumentDialog = (item) => {
    // 创建一个新对象，只包含必要的信息，不包含文件列表
    const cleanItem = {
      kb_id: item.kb_id,         // 添加 kb_id
      kb_name: item.kb_name,
      description: item.description,
      file_count: item.file_count
    };

    // 先设置 form，再设置 show，避免 watch 触发两次
    dialogStore.fileProcessForm = cleanItem;
    dialogStore.fileProcessShow = true;
  };

  // 处理文档处理完成后的操作
  const handleProcessComplete = async () => {
    // 刷新知识库列表以获取最新的文件数量
    await fetchKnowledgeBases();
  };

  // 监听文件处理对话框关闭
  watch(fileProcessShow, async (newVal, oldVal) => {
    if (oldVal && !newVal) {
      // 文件处理对话框关闭时刷新知识库列表
      await fetchKnowledgeBases();
    }
  });
  
  // 监听知识库操作完成
  watch(addKnowledgeShow, async (newVal, oldVal) => {
    // 当对话框从打开变为关闭时，刷新列表
    if (oldVal && !newVal) {
      await fetchKnowledgeBases();
    }
  });

  // 初始化
  onMounted(() => {
    fetchKnowledgeBases();
  });

  // Open the lineage dialog by changing Pinia store state
  const openLineageDialog = () => {
    lineageDialogShow.value = true;
  };
</script>

<style scoped>
  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }

  .items {
    background-image: url("/assets/images/知识库底图.png");
    width: 502px;
    height: 309px;
    position: relative;
    margin: 10px;
  }

  .kb-header {
    position: absolute;
    left: 100px;
    top: 20px;
    color: #fff;
  }

  .kb-name {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .kb-date {
    margin-top: 10px;
    font-size: 14px;
  }

  .kb-description {
    position: absolute;
    left: 40px;
    top: 100px;
    height: 100px;
    width: 440px;
    color: #0084ff;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .kb-files {
    position: absolute;
    left: 20px;
    top: 200px;
    display: flex;
    align-items: center;
    color: #0084ff;
  }

  .kb-actions {
    position: absolute;
    bottom: 25px;
    right: 40px;
    color: #0084ff;
  }

  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .fixed-nav {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    margin-bottom: 10px;
    flex-shrink: 0;
  }

  .scrollable-content {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
  }

  .progress-info {
    text-align: center;
    padding: 20px;
  }

  .progress-info p {
    margin-top: 10px;
    color: #606266;
  }
</style>
