package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.dto.file.FileDeleteDto;
import com.ai.aichat.model.vo.response.KnowledgeBaseFileVo;
import com.ai.aichat.service.KnowledgeBaseFileService;
import com.ai.aichat.service.KnowledgeBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 文件管理控制器
 */
@Slf4j
@Tag(name = "文件管理", description = "知识库文件管理相关接口")
@RequiredArgsConstructor
@RestController
public class FileManagementController {

    private final KnowledgeBaseFileService knowledgeBaseFileService;
    private final KnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "上传文件到知识库")
    @PostMapping({"/upload_file", "/upload_docs"})
    public BaseResponse<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "kb_id") Long kbId,
            @RequestParam(value = "kb_name") String kbName) {

        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");
        ThrowUtils.throwIf(kbId == null, ErrorCode.PARAMS_ERROR, "知识库ID不能为空");
        ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "知识库名称不能为空");

        Map<String, Object> result = knowledgeBaseFileService.uploadFile(file, kbId, kbName);
        return ResultUtils.success(result);
    }

    @Operation(summary = "从知识库删除文件")
    @DeleteMapping("/remove_file_from_kb")
    public BaseResponse<Boolean> removeFile(@RequestBody FileDeleteDto request) {
        ThrowUtils.throwIf(request == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        ThrowUtils.throwIf(request.getKbId() == null, ErrorCode.PARAMS_ERROR, "知识库ID不能为空");
        ThrowUtils.throwIf(request.getKbName() == null || request.getKbName().trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "知识库名称不能为空");
        ThrowUtils.throwIf(request.getFileName() == null || request.getFileName().trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "文件名不能为空");

        Boolean result = knowledgeBaseFileService.removeFile(request.getKbId(), request.getKbName(), request.getFileName());
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取知识库文件列表")
    @GetMapping("/knowledge_base_files")
    public BaseResponse<Map<String, Object>> getKnowledgeBaseFiles(
            @Parameter(description = "知识库ID")
            @RequestParam(value = "kb_id", required = false) Long kbId,
            @Parameter(description = "知识库名称")
            @RequestParam(value = "kb_name", required = false) String kbName,
            @Parameter(description = "是否包含文件详情")
            @RequestParam(value = "include_files", defaultValue = "true") Boolean includeFiles,
            @Parameter(description = "页码")
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {

        ThrowUtils.throwIf(kbId == null && (kbName == null || kbName.trim().isEmpty()),
                ErrorCode.PARAMS_ERROR, "知识库ID或名称不能为空");
        ThrowUtils.throwIf(page < 1, ErrorCode.PARAMS_ERROR, "页码必须大于0");
        ThrowUtils.throwIf(pageSize < 1 || pageSize > 100, ErrorCode.PARAMS_ERROR, "每页大小必须在1-100之间");

        Map<String, Object> result = knowledgeBaseFileService.getKnowledgeBaseFilesPaged(kbId, kbName, page, pageSize);
        return ResultUtils.success(result);
    }

    @Operation(summary = "处理文档（向量化）")
    @PostMapping("/process_documents")
    public BaseResponse<Map<String, Object>> processDocuments(@RequestBody Map<String, Object> request) {
        ThrowUtils.throwIf(request == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");

        Long kbId = null;
        if (request.get("kb_id") != null) {
            try {
                kbId = Long.valueOf(request.get("kb_id").toString());
            } catch (Exception e) {
                ThrowUtils.throwIf(true, ErrorCode.PARAMS_ERROR, "知识库ID格式错误");
            }
        }
        String kbName = (String) request.get("kb_name");
        @SuppressWarnings("unchecked")
        List<String> fileNames = (List<String>) request.get("file_names");

        ThrowUtils.throwIf(kbId == null && (kbName == null || kbName.trim().isEmpty()),
                ErrorCode.PARAMS_ERROR, "知识库ID或名称不能为空");
        ThrowUtils.throwIf(fileNames == null || fileNames.isEmpty(),
                ErrorCode.PARAMS_ERROR, "文件名列表不能为空");

        Map<String, Object> result = knowledgeBaseFileService.processDocuments(kbId, kbName, fileNames);
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取处理进度")
    @GetMapping("/process_progress")
    public BaseResponse<Map<String, Object>> getProcessProgress(
            @Parameter(description = "知识库名称", required = true)
            @RequestParam("kb_name") String kbName) {

        ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "知识库名称不能为空");

        Map<String, Object> progress = knowledgeBaseFileService.getProcessProgress(kbName);
        return ResultUtils.success(progress);
    }

    @Operation(summary = "获取文件信息")
    @GetMapping("/get_file_info")
    public BaseResponse<KnowledgeBaseFileVo> getFileInfo(
            @Parameter(description = "知识库名称", required = true)
            @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名", required = true)
            @RequestParam("file_name") String fileName) {

        ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "知识库名称不能为空");
        ThrowUtils.throwIf(fileName == null || fileName.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "文件名不能为空");

        KnowledgeBaseFileVo fileInfo = knowledgeBaseFileService.getFileInfo(kbName, fileName);
        return ResultUtils.success(fileInfo);
    }

    @Operation(summary = "通用文件上传接口")
    @PostMapping("/common/upload")
    public BaseResponse<Map<String, Object>> commonUpload(
            @Parameter(description = "上传的文件", required = true)
            @RequestParam("file") MultipartFile file) {

        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");

        Map<String, Object> result = knowledgeBaseFileService.commonUpload(file);
        return ResultUtils.success(result);
    }
}
