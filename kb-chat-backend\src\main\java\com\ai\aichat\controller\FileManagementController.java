package com.ai.aichat.controller;

import com.ai.aichat.model.dto.file.FileDeleteDto;
import com.ai.aichat.model.entity.KnowledgeBase;
import com.ai.aichat.model.vo.response.KnowledgeBaseFileVo;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.ai.aichat.service.FileStorageService;
import com.ai.aichat.service.KnowledgeBaseFileService;
import com.ai.aichat.service.KnowledgeBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件管理控制器
 */
@Slf4j
@Tag(name = "文件管理", description = "知识库文件管理相关接口")
@RequiredArgsConstructor
@RestController
public class FileManagementController {

    private final KnowledgeBaseFileService knowledgeBaseFileService;
    private final KnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "上传文件到知识库")
    @PostMapping({"/upload_file", "/upload_docs"})
    public StreamResponseVo uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "kb_id") Long kbId,
            @RequestParam(value = "kb_name") String kbName) {

        log.info("上传文件: {} - {} - {}", kbId, kbName, file.getOriginalFilename());
        Map<String, Object> result = knowledgeBaseFileService.uploadFile(file, kbId, kbName);

        return StreamResponseVo.builder()
                .code(1)
                .msg("文件上传成功")
                .data(result)
                .build();
    }

    @Operation(summary = "从知识库删除文件")
    @DeleteMapping("/remove_file_from_kb")
    public StreamResponseVo removeFile(@RequestBody FileDeleteDto  request) {
        log.info("删除文件请求: {}", request);
        Boolean result = knowledgeBaseFileService.removeFile(request.getKbId(),request.getKbName(), request.getFileName());

        if(!result){
            return StreamResponseVo.error("文件删除失败");
        }
        return StreamResponseVo.builder()
                .code(1)
                .msg("success")
                .data("")
                .build();
    }

    @Operation(summary = "获取知识库文件列表")
    @GetMapping("/knowledge_base_files")
    public StreamResponseVo getKnowledgeBaseFiles(
            @Parameter(description = "知识库ID")
            @RequestParam(value = "kb_id", required = false) Long kbId,
            @Parameter(description = "知识库名称")
            @RequestParam(value = "kb_name", required = false) String kbName,
            @Parameter(description = "是否包含文件详情")
            @RequestParam(value = "include_files", defaultValue = "true") Boolean includeFiles,
            @Parameter(description = "页码")
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取知识库文件列表: kb_id={}, kb_name={}, page: {}, pageSize: {}", kbId, kbName, page, pageSize);

            if (kbId == null && (kbName == null || kbName.trim().isEmpty())) {
                return StreamResponseVo.error("知识库ID或名称不能为空");
            }

            List<KnowledgeBaseFileVo> allFiles;
            if (kbId != null) {
                allFiles = knowledgeBaseFileService.getKnowledgeBaseFilesByKbId(kbId);
            } else {
                allFiles = knowledgeBaseFileService.getKnowledgeBaseFiles(kbName);
            }

            // 简单的内存分页（生产环境建议在数据库层面分页）
            int total = allFiles.size();
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<KnowledgeBaseFileVo> pagedFiles = startIndex < total ?
                allFiles.subList(startIndex, endIndex) : List.of();

            Map<String, Object> result = Map.of(
                "files", pagedFiles,
                "total", total,
                "page", page,
                "page_size", pageSize,
                "total_pages", (int) Math.ceil((double) total / pageSize)
            );

            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(result)
                    .build();

        } catch (Exception e) {
            log.error("获取知识库文件列表失败: {}", kbName, e);
            return StreamResponseVo.error("获取知识库文件列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "处理文档（向量化）")
    @PostMapping("/process_documents")
    public StreamResponseVo processDocuments(@RequestBody Map<String, Object> request) {
        try {
            Long kbId = null;
            if (request.get("kb_id") != null) {
                try {
                    kbId = Long.valueOf(request.get("kb_id").toString());
                } catch (Exception ignore) {}
            }
            String kbName = (String) request.get("kb_name");
            @SuppressWarnings("unchecked")
            List<String> fileNames = (List<String>) request.get("file_names");
            if ((kbId == null && (kbName == null || kbName.trim().isEmpty()))) {
                return StreamResponseVo.error("知识库ID或名称不能为空");
            }
            if (fileNames == null || fileNames.isEmpty()) {
                return StreamResponseVo.error("文件名列表不能为空");
            }
            log.info("处理文档: kb_id={}, kb_name={}, files={}", kbId, kbName, fileNames);
            Map<String, Object> result = knowledgeBaseFileService.processDocuments(kbId, kbName, fileNames);
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("文档处理已开始")
                    .data(result)
                    .build();
        } catch (Exception e) {
            log.error("文档处理失败", e);
            return StreamResponseVo.error("文档处理失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取处理进度")
    @GetMapping("/process_progress")
    public StreamResponseVo getProcessProgress(
            @Parameter(description = "知识库名称", required = true)
            @RequestParam("kb_name") String kbName) {
        try {
            log.info("获取处理进度: {}", kbName);
            
            Map<String, Object> progress = knowledgeBaseFileService.getProcessProgress(kbName);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(progress)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取处理进度失败: {}", kbName, e);
            return StreamResponseVo.error("获取处理进度失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取文件信息")
    @GetMapping("/get_file_info")
    public StreamResponseVo getFileInfo(
            @Parameter(description = "知识库名称", required = true)
            @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名", required = true)
            @RequestParam("file_name") String fileName) {
        try {
            log.info("获取文件信息: {} - {}", kbName, fileName);
            
            KnowledgeBaseFileVo fileInfo = knowledgeBaseFileService.getFileInfo(kbName, fileName);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(fileInfo)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取文件信息失败: {} - {}", kbName, fileName, e);
            return StreamResponseVo.error("获取文件信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "通用文件上传接口")
    @PostMapping("/common/upload")
    public StreamResponseVo commonUpload(
            @Parameter(description = "上传的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("通用文件上传: {}", file.getOriginalFilename());
            
            // 这里可以实现通用的文件上传逻辑
            // 目前返回模拟结果，与前端FileUpload组件兼容
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("文件上传成功")
                    .data(Map.of(
                        "fileName", file.getOriginalFilename(),
                        "url", "/files/" + file.getOriginalFilename(),
                        "size", file.getSize()
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("通用文件上传失败", e);
            return StreamResponseVo.error("文件上传失败: " + e.getMessage());
        }
    }
}
