package com.ai.aichat.service;

import com.ai.aichat.model.dto.request.TrainingRequestDto;
import com.ai.aichat.model.entity.TrainingTask;
import com.ai.aichat.model.vo.response.TrainingTaskVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 训练任务服务接口
 */
public interface TrainingTaskService extends IService<TrainingTask> {

    /**
     * 开始训练任务
     * @param requestDto 训练请求参数
     * @return 训练任务信息
     */
    TrainingTaskVo startTraining(TrainingRequestDto requestDto);

    /**
     * 获取训练任务列表
     * @return 训练任务列表
     */
    List<TrainingTaskVo> getTrainingTasks();

    /**
     * 根据ID获取训练任务
     * @param id 任务ID
     * @return 训练任务信息
     */
    TrainingTaskVo getTrainingTaskById(Long id);

    /**
     * 获取最新的训练任务
     * @return 最新训练任务信息
     */
    TrainingTaskVo getLatestTrainingTask();

    /**
     * 更新训练任务状态
     * @param taskId 任务ID
     * @param status 训练状态
     * @param currentEpoch 当前轮次
     * @param loss 训练损失
     * @param accuracy 训练精度
     * @param progress 训练进度
     * @return 是否更新成功
     */
    Boolean updateTrainingTaskStatus(Long taskId, Integer status, Integer currentEpoch, 
                                   BigDecimal loss, BigDecimal accuracy, BigDecimal progress);

    /**
     * 更新训练任务日志
     * @param taskId 任务ID
     * @param logContent 训练日志
     * @return 是否更新成功
     */
    Boolean updateTrainingTaskLog(Long taskId, String logContent);

    /**
     * 完成训练任务
     * @param taskId 任务ID
     * @param success 是否成功
     * @param errorMessage 错误信息（如果失败）
     * @return 是否更新成功
     */
    Boolean completeTrainingTask(Long taskId, Boolean success, String errorMessage);

    /**
     * 取消训练任务
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    Boolean cancelTrainingTask(Long taskId);

}
