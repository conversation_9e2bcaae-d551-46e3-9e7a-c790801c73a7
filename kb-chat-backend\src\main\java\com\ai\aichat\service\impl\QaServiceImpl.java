package com.ai.aichat.service.impl;

import com.ai.aichat.mapper.QaBaseMapper;
import com.ai.aichat.mapper.QaFileMapper;
import com.ai.aichat.mapper.QaPairMapper;
import com.ai.aichat.model.dto.qa.*;
import com.ai.aichat.model.entity.QaBase;
import com.ai.aichat.model.entity.QaFile;
import com.ai.aichat.model.entity.QaPair;
import com.ai.aichat.model.vo.qa.QaBaseVo;
import com.ai.aichat.model.vo.qa.QaHistoryVo;
import com.ai.aichat.model.vo.qa.QaListVo;
import com.ai.aichat.service.QaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问答对服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QaServiceImpl implements QaService {

    private final QaBaseMapper qaBaseMapper;
    private final QaFileMapper qaFileMapper;
    private final QaPairMapper qaPairMapper;
    private final ObjectMapper objectMapper;

    @Value("${app.qa.base-dir:./data/qa_base}")
    private String qaBaseDir;

    @Override
    public QaListVo listQa() {
        // 从数据库获取所有问答库
        List<QaBase> qaBases = qaBaseMapper.selectList(new LambdaQueryWrapper<>());
        
        List<QaBaseVo> qaBaseVos = new ArrayList<>();
        
        for (QaBase qaBase : qaBases) {
            QaBaseVo vo = new QaBaseVo();
            vo.setKbName(qaBase.getName());
            vo.setDescription(qaBase.getDescription());
            vo.setCreatedAt(qaBase.getCreateTime());
            
            // 获取该问答库的文件列表
            List<QaFile> qaFiles = qaFileMapper.selectList(
                new LambdaQueryWrapper<QaFile>()
                    .eq(QaFile::getQaBaseId, qaBase.getId())
            );
            
            if (!qaFiles.isEmpty()) {
                vo.setHasQaFiles(true);
                vo.setQaFiles(qaFiles.stream()
                    .map(QaFile::getFileName)
                    .collect(Collectors.toList()));

                // 获取最新文件日期
                Optional<Date> latestDate = qaFiles.stream()
                    .map(QaFile::getCreateTime)
                    .max(Date::compareTo);

                if (latestDate.isPresent()) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    vo.setLatestFileDate(sdf.format(latestDate.get()));
                }
            } else {
                vo.setHasQaFiles(false);
                vo.setQaFiles(new ArrayList<>());
                vo.setLatestFileDate(null);
            }
            
            qaBaseVos.add(vo);
        }
        
        QaListVo result = new QaListVo();
        result.setKbList(qaBaseVos);
        return result;
    }

    @Override
    @Transactional
    public String createQa(QaBaseCreateDto dto) {
        // 检查问答库是否已存在
        QaBase existing = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, dto.getKbName())
        );

        if (existing != null) {
            throw new RuntimeException("问答库已存在: " + dto.getKbName());
        }

        // 创建问答库记录
        QaBase qaBase = new QaBase();
        qaBase.setName(dto.getKbName());
        qaBase.setDescription(dto.getDescription());
        qaBase.setUserId(1L); // 默认用户ID
        
        qaBaseMapper.insert(qaBase);
        
        // 创建文件系统目录
        try {
            Path qaDir = Paths.get(qaBaseDir, dto.getKbName());
            Files.createDirectories(qaDir);
            
            // 创建metadata.json文件
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("description", dto.getDescription());
            metadata.put("created_at", new Date());
            
            Path metadataPath = qaDir.resolve("metadata.json");
            objectMapper.writeValue(metadataPath.toFile(), metadata);
            
        } catch (IOException e) {
            log.error("创建问答库目录失败: {}", e.getMessage());
            throw new RuntimeException("创建问答库目录失败: " + e.getMessage());
        }
        
        return "问答库创建成功";
    }

    @Override
    @Transactional
    public String updateQa(QaBaseUpdateDto dto) {
        // 查找原问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, dto.getKbName())
        );

        if (qaBase == null) {
            throw new RuntimeException("问答库不存在: " + dto.getKbName());
        }

        // 如果要修改名称，检查新名称是否已存在
        if (StringUtils.hasText(dto.getNewKbName()) && !dto.getNewKbName().equals(dto.getKbName())) {
            QaBase existing = qaBaseMapper.selectOne(
                new LambdaQueryWrapper<QaBase>()
                    .eq(QaBase::getName, dto.getNewKbName())
            );

            if (existing != null) {
                throw new RuntimeException("新问答库名称已存在: " + dto.getNewKbName());
            }

            qaBase.setName(dto.getNewKbName());
        }
        
        if (StringUtils.hasText(dto.getDescription())) {
            qaBase.setDescription(dto.getDescription());
        }
        
        qaBaseMapper.updateById(qaBase);
        
        return "问答库更新成功";
    }

    @Override
    @Transactional
    public String deleteQaBase(QaBaseDeleteDto dto) {
        // 查找问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, dto.getKbName())
        );

        if (qaBase == null) {
            throw new RuntimeException("问答库不存在: " + dto.getKbName());
        }

        // 先删除所有相关的问答对文件记录（级联删除会自动处理qa_pair表）
        List<QaFile> qaFiles = qaFileMapper.selectList(
            new LambdaQueryWrapper<QaFile>()
                .eq(QaFile::getQaBaseId, qaBase.getId())
        );

        // 删除所有文件记录（逻辑删除，会级联删除相关的qa_pair记录）
        for (QaFile qaFile : qaFiles) {
            qaFileMapper.deleteById(qaFile.getId());
        }

        // 删除问答库（逻辑删除）
        qaBaseMapper.deleteById(qaBase.getId());

        // 删除文件系统目录
        try {
            Path qaDir = Paths.get(qaBaseDir, dto.getKbName());
            if (Files.exists(qaDir)) {
                deleteDirectory(qaDir.toFile());
            }
        } catch (Exception e) {
            log.error("删除问答库目录失败: {}", e.getMessage());
        }

        return "问答库删除成功";
    }

    @Override
    public QaHistoryVo getQaHistory(String name, String fileName) {
        // 查找问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, name)
        );

        if (qaBase == null) {
            throw new RuntimeException("问答库不存在: " + name);
        }

        // 查找文件
        QaFile qaFile = qaFileMapper.selectOne(
            new LambdaQueryWrapper<QaFile>()
                .eq(QaFile::getQaBaseId, qaBase.getId())
                .eq(QaFile::getFileName, fileName)
        );

        if (qaFile == null) {
            throw new RuntimeException("文件不存在: " + fileName);
        }

        QaHistoryVo result = new QaHistoryVo();
        result.setKbName(name);
        result.setFileName(fileName);

        // 直接读取文件内容
        try {
            Path filePath = Paths.get(qaFile.getFilePath());
            if (!Files.exists(filePath)) {
                throw new RuntimeException("文件不存在: " + filePath);
            }

            String fileContent = Files.readString(filePath);

            // 尝试解析JSON内容
            List<QaHistoryVo.QaPairVo> qaPairVos = new ArrayList<>();
            try {
                List<Map<String, Object>> qaPairData = objectMapper.readValue(fileContent, new TypeReference<List<Map<String, Object>>>() {});
                qaPairVos = qaPairData.stream()
                    .map(qaPairMap -> {
                        QaHistoryVo.QaPairVo vo = new QaHistoryVo.QaPairVo();
                        vo.setInstruction((String) qaPairMap.get("instruction"));
                        vo.setInput((String) qaPairMap.get("input"));
                        vo.setOutput((String) qaPairMap.get("output"));
                        return vo;
                    })
                    .collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("无法解析文件内容为JSON格式，返回原始内容: {}", e.getMessage());
                // 如果不是JSON格式，创建一个包含原始内容的问答对
                QaHistoryVo.QaPairVo vo = new QaHistoryVo.QaPairVo();
                vo.setInstruction("文件内容");
                vo.setInput("");
                vo.setOutput(fileContent);
                qaPairVos.add(vo);
            }

            result.setQaPairs(qaPairVos);

        } catch (IOException e) {
            log.error("读取文件失败: {}", e.getMessage());
            throw new RuntimeException("读取文件失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional
    public String deleteQa(String name, String fileName) {
        // 查找问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, name)
        );
        
        if (qaBase == null) {
            throw new RuntimeException("问答库不存在: " + name);
        }
        
        // 查找并删除文件
        QaFile qaFile = qaFileMapper.selectOne(
            new LambdaQueryWrapper<QaFile>()
                .eq(QaFile::getQaBaseId, qaBase.getId())
                .eq(QaFile::getFileName, fileName)
        );
        
        if (qaFile == null) {
            throw new RuntimeException("文件不存在: " + fileName);
        }
        
        // 删除文件记录（逻辑删除）
        qaFileMapper.deleteById(qaFile.getId());
        
        // 删除物理文件
        try {
            Path filePath = Paths.get(qaFile.getFilePath());
            if (Files.exists(filePath)) {
                Files.delete(filePath);
            }
        } catch (IOException e) {
            log.error("删除物理文件失败: {}", e.getMessage());
        }
        
        return "文件删除成功";
    }

    @Override
    @Transactional
    public String saveChat(SaveChatDto dto) {
        // 查找或创建问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, dto.getKbName())
        );

        if (qaBase == null) {
            // 问答库不存在
            return "问答库不存在！";
        }

        // 生成文件名
        String fileName = StringUtils.hasText(dto.getDatasetName()) ?
            dto.getDatasetName() + ".json" :
            "qa_data_" + System.currentTimeMillis() + ".json";

        // 检查文件是否已存在，如果存在则删除旧记录
        QaFile existingFile = qaFileMapper.selectOne(
            new LambdaQueryWrapper<QaFile>()
                .eq(QaFile::getQaBaseId, qaBase.getId())
                .eq(QaFile::getFileName, fileName)
        );

        if (existingFile != null) {
            // 删除旧的文件记录
            qaFileMapper.deleteById(existingFile.getId());

            // 删除旧的物理文件
            try {
                Path oldFilePath = Paths.get(existingFile.getFilePath());
                if (Files.exists(oldFilePath)) {
                    Files.delete(oldFilePath);
                }
            } catch (IOException e) {
                log.warn("删除旧文件失败: {}", e.getMessage());
            }
        }

        // 创建文件记录
        QaFile qaFile = new QaFile();
        qaFile.setQaBaseId(qaBase.getId());
        qaFile.setFileName(fileName);
        qaFile.setOriginalName(fileName);
        qaFile.setQaPairCount(0); // 不解析内容，设置为0
        qaFile.setDescription(dto.getDescription());
        qaFile.setUserId(1L);

        // 保存到文件系统
        try {
            Path qaDir = Paths.get(qaBaseDir, dto.getKbName());
            Files.createDirectories(qaDir);

            Path filePath = qaDir.resolve(fileName);
            // 直接写入文件内容，不进行JSON解析
            Files.write(filePath, dto.getContent().getBytes());

            qaFile.setFilePath(filePath.toString());
            qaFile.setFileSize(Files.size(filePath));

        } catch (IOException e) {
            log.error("保存问答对文件失败: {}", e.getMessage());
            throw new RuntimeException("保存问答对文件失败: " + e.getMessage());
        }
        
        qaFileMapper.insert(qaFile);

        return "问答对保存成功";
    }

    @Override
    public boolean checkFileExists(String kbName, String fileName) {
        // 查找问答库
        QaBase qaBase = qaBaseMapper.selectOne(
            new LambdaQueryWrapper<QaBase>()
                .eq(QaBase::getName, kbName)
        );

        if (qaBase == null) {
            return false;
        }

        // 检查文件是否存在
        QaFile existingFile = qaFileMapper.selectOne(
            new LambdaQueryWrapper<QaFile>()
                .eq(QaFile::getQaBaseId, qaBase.getId())
                .eq(QaFile::getFileName, fileName)
        );

        return existingFile != null;
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
