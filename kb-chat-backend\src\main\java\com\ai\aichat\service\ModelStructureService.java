package com.ai.aichat.service;

import com.ai.aichat.model.entity.ModelStructure;
import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 模型结构服务接口
 */
public interface ModelStructureService extends IService<ModelStructure> {

    /**
     * 获取所有模型结构列表
     * @return 模型结构列表
     */
    List<ModelStructureVo> getAllModelStructures();

    /**
     * 根据ID获取模型结构
     * @param id 模型ID
     * @return 模型结构信息
     */
    ModelStructureVo getModelStructureById(Long id);

    /**
     * 更新模型训练状态
     * @param modelId 模型ID
     * @param status 训练状态
     * @return 是否更新成功
     */
    Boolean updateTrainingStatus(Long modelId, Integer status);

    /**
     * 创建模型结构
     * @param version 模型版本
     * @param baseModel 基础模型
     * @param dataset 数据集
     * @param description 描述
     * @param modelPath 模型路径
     * @return 是否创建成功
     */
    Boolean createModelStructure(String version, String baseModel, String dataset, String description, String modelPath);

    /**
     * 更新模型结构
     * @param id 模型ID
     * @param version 模型版本
     * @param baseModel 基础模型
     * @param dataset 数据集
     * @param description 描述
     * @param modelPath 模型路径
     * @return 是否更新成功
     */
    Boolean updateModelStructure(Long id, String version, String baseModel, String dataset, String description, String modelPath);

    /**
     * 删除模型结构
     * @param id 模型ID
     * @return 是否删除成功
     */
    Boolean deleteModelStructure(Long id);

    /**
     * 上传模型文件夹
     * @param folderName 文件夹名称
     * @param files 文件映射
     * @return 上传结果
     */
    Map<String, Object> uploadModelFolder(String folderName, Map<String, MultipartFile> files);

    /**
     * 获取导出路径
     * @return 导出路径信息
     */
    Map<String, String> getExportPath();

    /**
     * 导出模型
     * @param id 模型ID
     * @return 导出结果
     */
    Map<String, Object> exportModel(Long id);

}
