package com.ai.aichat.service.impl;

import com.ai.aichat.constants.RedisConstants;
import com.ai.aichat.exception.BusinessException;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.mapper.KnowledgeBaseMapper;
import com.ai.aichat.model.entity.KnowledgeBase;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.ai.aichat.model.vo.response.KnowledgeBaseVo;
import com.ai.aichat.service.FileStorageService;
import com.ai.aichat.service.KnowledgeBaseFileService;
import com.ai.aichat.service.KnowledgeBaseService;
import com.ai.aichat.service.MilvusService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 知识库服务实现类
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> implements KnowledgeBaseService {

    private final FileStorageService fileStorageService;
    private final MilvusService milvusService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;
    private final KnowledgeBaseFileService knowledgeBaseFileService;

    @Autowired
    public KnowledgeBaseServiceImpl(@Lazy KnowledgeBaseFileService knowledgeBaseFileService,
                                    FileStorageService fileStorageService,
                                    MilvusService milvusService,
                                    ObjectMapper objectMapper,
                                    StringRedisTemplate stringRedisTemplate) {
        this.knowledgeBaseFileService = knowledgeBaseFileService;
        this.fileStorageService = fileStorageService;
        this.milvusService = milvusService;
        this.objectMapper = objectMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public List<String> listKnowledgeBaseNames() {
        QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0)
                   .orderByAsc("name");

        List<KnowledgeBase> knowledgeBases = list(queryWrapper);

        return knowledgeBases.stream()
                .map(KnowledgeBase::getName)
                .collect(Collectors.toList());
    }

    @Override
    public List<KnowledgeBaseVo> getAllKnowledgeBases() {
        // 1.先查询redis
        String kbJson = stringRedisTemplate.opsForValue().get(RedisConstants.KB_LIST_KEY);
        if (kbJson != null && !kbJson.isEmpty()) {
            try {
                return objectMapper.readValue(kbJson, new TypeReference<List<KnowledgeBaseVo>>() {});
            } catch (Exception e) {
                log.error("反序列化知识库列表失败", e);
            }
        }
        // 2.如果redis没有，则从数据库中查询
        try {
            QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_delete", 0)
                       .orderByDesc("update_time");

            List<KnowledgeBase> knowledgeBases = list(queryWrapper);
            List<KnowledgeBaseVo> voList = knowledgeBases.stream()
                    .map(this::convertToVo)
                    .collect(Collectors.toList());
            if (voList != null && !voList.isEmpty()) {
                try {
                    String json = objectMapper.writeValueAsString(voList);
//                    stringRedisTemplate.opsForValue().set(RedisConstants.KB_LIST_KEY, json, RedisConstants.KB_NAME_TTL, TimeUnit.MINUTES);
                } catch (Exception e) {
                    log.error("序列化知识库列表失败", e);
                }
            }
            log.info("获取所有知识库信息成功并缓存到redis");
            return voList;
        } catch (Exception e) {
            log.error("获取所有知识库信息失败", e);
            throw new RuntimeException("获取所有知识库信息失败: " + e.getMessage());
        }
    }

    @Override
    public KnowledgeBase getKnowledgeBaseEntityByName(String name) {
        try {
            QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", name)
                       .eq("is_delete", 0);

            return getOne(queryWrapper);

        } catch (Exception e) {
            log.error("获取知识库实体失败: {}", name, e);
            throw new RuntimeException("获取知识库实体失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Boolean createKnowledgeBase(String name, String description) {
        if (name == null || name.trim().isEmpty()) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "知识库名称不能为空");
        }
        // 检查知识库是否已存在
        QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name)
                   .eq("is_delete", 0);

        if (getOne(queryWrapper) != null) {
            throw new BusinessException(ErrorCode.OPERATION_ERROR,"知识库已存在: " + name);
        }

        // 1.创建新知识库
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setName(name);
        knowledgeBase.setDescription(description != null ? description : "");
        knowledgeBase.setUserId(1L); // 默认用户ID，可以根据实际需求修改

        boolean result = save(knowledgeBase);

        // 2.创建文件夹结构
        if (result) {
            boolean dirResult = fileStorageService.createKnowledgeBaseDirectories(name);
            if (!dirResult) {
                log.warn("知识库数据库记录创建成功，但文件夹创建失败: {}", name);
            }
        }

        // 3.创建向量数据库 - 现在不需要预先创建，在处理文档时再创建
        // String collectionName = "kb_" + knowledgeBase.getId();
        // milvusService.hasCollection(collectionName, name);

        // 4.删除redis缓存
        stringRedisTemplate.delete(RedisConstants.KB_LIST_KEY);

        log.info("创建知识库成功: {}", name);
        return result;
    }

    @Override
    @Transactional
    public Boolean deleteKnowledgeBase(Long id) {
        // 1. 删除所有文件
        Integer deletedFileCount = knowledgeBaseFileService.removeAllFilesByKbId(id);
        log.info("删除了 {} 个文件", deletedFileCount);

        // 2. 删除向量collection
        milvusService.deleteCollection(id);

        // 3. 删除知识库
        KnowledgeBase knowledgeBase = this.getKnowledgeBaseEntityById(id);
        String name = knowledgeBase.getName();
        // 3.1. 删除知识库记录（逻辑删除）
        // 注意：文件的删除将由调用方（Controller或其他Service）负责
        boolean result = removeById(knowledgeBase.getId());

        // 3.2. 删除文件夹结构
        if (result) {
            boolean dirResult = fileStorageService.deleteKnowledgeBaseDirectories(name);
            if (!dirResult) {
                log.warn("知识库数据库记录删除成功，但文件夹删除失败: {}", name);
            }
        }

        // 4. 删除Redis缓存
        stringRedisTemplate.delete(RedisConstants.KB_LIST_KEY);

        log.info("删除知识库成功: {} (ID: {})", name, knowledgeBase.getId());
        return result;
    }

    @Override
    @Transactional
    public Boolean updateKnowledgeBase(Long kbId, String newName, String description) {
        try {
            KnowledgeBase knowledgeBase = getById(kbId);
            if (knowledgeBase == null) {
                throw new BusinessException(ErrorCode.OPERATION_ERROR,"知识库不存在: " + kbId);
            }
            boolean nameChanged = newName != null && !newName.trim().isEmpty() && !newName.equals(knowledgeBase.getName());
            boolean descChanged = description != null && !description.equals(knowledgeBase.getDescription());
            if (!nameChanged && !descChanged) {
                return false;
            }
            // 检查新名称是否已存在
            if (nameChanged) {
                QueryWrapper<KnowledgeBase> checkWrapper = new QueryWrapper<>();
                checkWrapper.eq("name", newName)
                           .eq("is_delete", 0);
                KnowledgeBase existingKb = getOne(checkWrapper);
                if (existingKb != null) {
                    throw new BusinessException(ErrorCode.OPERATION_ERROR,"知识库名称已存在: " + newName);
                }
                String oldName = knowledgeBase.getName();
                knowledgeBase.setName(newName);
                // 重命名文件夹
                boolean renameResult = fileStorageService.renameKnowledgeBaseDirectory(oldName, newName);
                if (!renameResult) {
                    log.warn("知识库数据库更新成功，但文件夹重命名失败: {} -> {}", oldName, newName);
                }
                // 更新向量数据库描述
                // TODO: 这里milvus并没有提供这个功能

                // 删除Redis缓存
                stringRedisTemplate.delete(RedisConstants.KB_LIST_KEY);
            }
            if (descChanged) {
                knowledgeBase.setDescription(description);
            }
            knowledgeBase.setUpdateTime(new Date());
            boolean updateResult = updateById(knowledgeBase);
            if (!updateResult) {
                throw new RuntimeException("更新知识库失败");
            }
            log.info("更新知识库成功: {} (ID: {})", knowledgeBase.getName(), knowledgeBase.getId());
            return true;
        } catch (Exception e) {
            log.error("更新知识库失败: {}", kbId, e);
            throw new BusinessException(ErrorCode.OPERATION_ERROR,"更新知识库失败: " + e.getMessage());
        }
    }

    @Override
    public KnowledgeBase getKnowledgeBaseEntityById(Long kbId) {
        try {
            QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", kbId)
                    .eq("is_delete", 0);

            return getOne(queryWrapper);

        } catch (Exception e) {
            log.error("获取知识库实体失败: {}", kbId, e);
            throw new RuntimeException("获取知识库实体失败: " + e.getMessage());
        }
    }

    /**
     * 转换实体为VO
     */
    private KnowledgeBaseVo convertToVo(KnowledgeBase knowledgeBase) {
        KnowledgeBaseVo vo = new KnowledgeBaseVo();
        vo.setId(knowledgeBase.getId());           // 添加 ID 字段
        vo.setName(knowledgeBase.getName());
        vo.setDescription(knowledgeBase.getDescription());

        // 优先从数据库获取文件数量，如果为0则从文件系统获取（兼容旧数据）
        Integer fileCount = knowledgeBaseFileService.getFileCountByKbId(knowledgeBase.getId());
        if (fileCount == null || fileCount == 0) {
            // 如果数据库中没有记录，尝试从文件系统获取（兼容旧的知识库）
            fileCount = fileStorageService.getKnowledgeBaseFileCount(knowledgeBase.getName());
        }
        vo.setFile_count(fileCount != null ? fileCount : 0);

        // 向量数量暂时设为0，后续可以通过向量数据库查询
        vo.setVector_count(0);

        // 检查文件夹是否存在来确定状态
        boolean dirExists = fileStorageService.knowledgeBaseDirectoryExists(knowledgeBase.getName());
        vo.setStatus(dirExists ? "active" : "inactive");

        // 格式化时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (knowledgeBase.getCreateTime() != null) {
            vo.setCreated_at(sdf.format(knowledgeBase.getCreateTime()));
        }
        if (knowledgeBase.getUpdateTime() != null) {
            vo.setUpdated_at(sdf.format(knowledgeBase.getUpdateTime()));
        }

        return vo;
    }
}
