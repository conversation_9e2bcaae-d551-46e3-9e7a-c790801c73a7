package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.service.MilvusService;
import com.ai.aichat.util.TikaUtil;
import com.ai.aichat.util.TikaVo;
import com.alibaba.fastjson.JSON;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.*;

@RestController
@RequestMapping("/api/vector")
@Tag(name = "向量数据库实践", description = "向量数据库实践")
@RequiredArgsConstructor
public class MilvusController {

    private final MilvusService milvusService;
    private final TikaUtil tikaUtil;
    private final ChatClient ragChatClient;
    private final OpenAiEmbeddingModel embeddingModel;

    @Autowired
    public MilvusController(
            OpenAiEmbeddingModel embeddingModel,
            MilvusService milvusService,
            TikaUtil tikaUtil,
            @Qualifier("ragChatClient") ChatClient ragChatClient) {
        this.embeddingModel = embeddingModel;
        this.milvusService = milvusService;
        this.tikaUtil = tikaUtil;
        this.ragChatClient = ragChatClient;
    }



    @Operation(summary = "添加自定义rag数据")
    @GetMapping("/addCustomRagData")
    public BaseResponse<InsertResp> addCustomRagData(@RequestParam String text) {
        ThrowUtils.throwIf(text == null || text.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "文本内容不能为空");
        InsertResp result = milvusService.addCustomRagData(text);
        return ResultUtils.success(result);
    }

    @Operation(summary = "搜索")
    @GetMapping("/search")
    public BaseResponse<SearchResp> search(@RequestParam String text) {
        ThrowUtils.throwIf(text == null || text.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "搜索文本不能为空");
        SearchResp result = milvusService.searchByText(text);
        return ResultUtils.success(result);
    }

    @Operation(summary = "解析文件内容")
    @PostMapping("/extractFileString")
    public BaseResponse<String> extractFileString(MultipartFile file) {
        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");
        String result = milvusService.extractFileString(file);
        return ResultUtils.success(result);
    }

    @Operation(summary = "解析文件内容-LangChain分片")
    @PostMapping("/splitParagraphsLangChain")
    public BaseResponse<List<String>> splitParagraphsLangChain(MultipartFile file) {
        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");
        List<String> result = milvusService.splitParagraphsLangChain(file);
        return ResultUtils.success(result);
    }

    @Operation(summary = "解析文件内容-HanLP分片")
    @PostMapping("/splitParagraphsHanLP")
    public BaseResponse<List<String>> splitParagraphsHanLP(MultipartFile file) {
        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");
        List<String> result = milvusService.splitParagraphsHanLP(file);
        return ResultUtils.success(result);
    }

    @Operation(summary = "上传知识库")
    @PostMapping("/uploadFile")
    public BaseResponse<InsertResp> uploadFile(MultipartFile file) {
        ThrowUtils.throwIf(file == null || file.isEmpty(), ErrorCode.PARAMS_ERROR, "文件不能为空");
        InsertResp result = milvusService.uploadFile(file);
        return ResultUtils.success(result);
    }



    @Operation(summary = "RAG问答")
    @PostMapping("/ask")
    public Flux<ChatResponse> askQuestion(@RequestBody String question) {
        // 调用 RAG 问答（自动结合向量检索和生成）
        return ragChatClient.prompt()
                .user(question)
                .stream()
                .chatResponse();
    }

    @Operation(summary = "测试RAG")
    @GetMapping("/test")
    public BaseResponse<String> testRag() {
        String result = ragChatClient.prompt()
                .user("西游记")
                .call()
                .content();
        return ResultUtils.success(result);
    }
}