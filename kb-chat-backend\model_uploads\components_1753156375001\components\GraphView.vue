<template>
  <div class="graph-view-container">
    <div class="preview-header">
      <div class="preview-title">知识图谱文件</div>
      <div class="close-preview-btn" @click="$emit('close')">
        <i class="el-icon-close"></i>
        <span>关闭预览</span>
      </div>
    </div>
    <div class="graph-content">
      <div
        class="drawBoard"
        ref="graphContainer"></div>
      <div class="info-box">
        <h3>节点信息</h3>
        <div v-if="selectedSummary">
          <h4>{{ selectedSummary.summary }}</h4>
          <p>{{ selectedSummary.explanation }}</p>
        </div>
        <div v-else>
          <p>点击节点或边以查看信息</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 导入 Vis.js 库
import { Network } from "vis-network/standalone/umd/vis-network.min.js";

export default {
  name: "GraphView",
  props: {
    // 图谱数据直接从父组件传入
    graphData: {
      type: Object,
      default: null
    }
  },
  emits: ['close'],
  
  data() {
    return {
      network: null,
      selectedSummary: null
    };
  },

  watch: {
    // 监听graphData变化，当有新数据时创建图谱
    graphData: {
      handler(newData) {
        if (newData) {
          this.$nextTick(() => {
            this.createGraph();
          });
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    createGraph() {
      if (!this.graphData) return;

      const container = this.$refs.graphContainer;
      if (!container) return; // 确保DOM元素已加载
      
      // 修改边的数据，确保正确处理边的属性
      const modifiedEdges = this.graphData.edges.map(edge => {
        return {
          id: edge.id,
          from: edge.source,  // 使用source作为起点
          to: edge.target,    // 使用target作为终点
          label: edge.label || '', // 使用label作为边标签
          title: edge.description || edge.label || '', // 使用description作为悬停提示
          arrows: 'to'
        };
      });

      const options = {
        height: "100%", // 高度自适应
        width: "100%",  // 宽度自适应
        nodes: {
          shape: "dot",
          size: 20,
          font: {
            color: "#ffffff",
            size: 14,
            face: 'arial',
            bold: false
          },
          borderWidth: 2,
          shadow: true
        },
        edges: {
          width: 2,
          shadow: true,
          font: {
            size: 14,
            color: "#ffffff",
            face: 'arial',
            background: {
              enabled: true,
              color: '#092762'
            },
            strokeWidth: 0
          },
          arrows: 'to',
          smooth: { type: "continuous" }
        },
        groups: {
          central: {
            color: {
              background: '#e04141',
              border: '#941e1e'
            }
          },
          related: {
            color: {
              background: '#4169e1',
              border: '#0000cd'
            }
          }
        },
        physics: {
          enabled: true,
          barnesHut: {
            gravitationalConstant: -2000,
            springLength: 200
          }
        }
      };

      // 创建网络图实例，使用修改后的边数据
      this.network = new Network(
        container,
        {
          nodes: this.graphData.nodes.map(node => ({
            id: node.id,
            label: node.label,
            title: node.description || '',
            group: node.isMainNode ? 'central' : 'related'
          })),
          edges: modifiedEdges
        },
        options
      );

      // 添加点击事件监听器
      this.network.on("click", (params) => {
        if (params.nodes.length > 0) {
          const nodeId = params.nodes[0];
          const node = this.graphData.nodes.find(n => n.id === nodeId);
          if (node) {
            this.selectedSummary = {
              summary: node.label,
              explanation: node.description || '无描述'
            };
          }
        } else if (params.edges.length > 0) {
          const edgeId = params.edges[0];
          const edge = modifiedEdges.find(e => e.id === edgeId) || 
                      this.graphData.edges.find(e => e.id === edgeId);
          if (edge) {
            this.selectedSummary = {
              summary: `关系: ${edge.label || ''}`,
              explanation: edge.title || edge.description || ''
            };
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.graph-view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(9, 39, 98, 0.95);
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
}

.graph-view-overlay {
  position: fixed;
  top: 0;
  right: 0;
  width: 45%;
  height: 100%;
  z-index: 200;
  background-color: rgba(9, 39, 98, 0.95);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.preview-header {
  height: 36px; /* 减小高度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px; /* 减小内边距 */
  background-color: rgba(9, 39, 98, 0.9);
  border-bottom: 1px solid #3f63a8;
}

.preview-title {
  font-weight: bold;
  color: #13fff3;
  font-size: 14px; /* 减小字体 */
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-title::before {
  content: '📊';
  font-size: 14px; /* 减小图标 */
}

.close-preview-btn {
  cursor: pointer;
  padding: 4px 8px; /* 减小内边距 */
  color: white;
  background-color: rgba(15, 45, 90, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 3px; /* 减小间距 */
  font-size: 13px; /* 减小字体 */
  transition: all 0.2s;
  z-index: 1010; /* 确保按钮在最上层 */
}

.close-preview-btn:hover {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
  border-color: #13fff3;
}

.graph-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawBoard {
  flex: 7;
  min-height: 0;
  background-color: #011b53;
  border: 1px solid #083271;
}

.info-box {
  flex: 3;
  min-height: 0;
  height: auto;
  max-height: none;
  padding: 10px;
  background-color: #011b53;
  color: white;
  overflow-y: auto;
  border-top: 1px solid #083271;
}

.info-box h3 {
  margin-top: 0;
  color: #13fff3;
  margin-bottom: 15px;
  border-bottom: 1px solid #3f63a8;
  padding-bottom: 5px;
}

.info-box h4 {
  color: #4169e1;
  margin-bottom: 10px;
}

.info-box p {
  line-height: 1.5;
  margin-bottom: 10px;
}
</style> 