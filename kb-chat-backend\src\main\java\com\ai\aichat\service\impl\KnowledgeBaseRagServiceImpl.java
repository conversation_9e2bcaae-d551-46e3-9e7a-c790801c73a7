package com.ai.aichat.service.impl;

import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.service.ConversationService;
import com.ai.aichat.service.KnowledgeBaseRagService;
import com.ai.aichat.service.KnowledgeBaseService;
import com.ai.aichat.model.entity.KnowledgeBase;
import com.ai.aichat.util.MilvusVectorStore;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import com.ai.aichat.model.vo.chat.SimpleChatResponseVo;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY;

/**
 * 知识库RAG问答服务实现
 */
@Slf4j
@Service
public class KnowledgeBaseRagServiceImpl implements KnowledgeBaseRagService {

    private final ChatClient chatClient;
    private final ConversationService conversationService;
    private final KnowledgeBaseService knowledgeBaseService;
    private final MilvusVectorStore milvusVectorStore;
    private final ObjectMapper objectMapper = new ObjectMapper(); // 可通过构造注入

    // 使用构造函数注入，并在构造函数中使用 @Qualifier 指定 bean 名称
    public KnowledgeBaseRagServiceImpl(
            @Qualifier("ragChatClient") ChatClient chatClient,
            KnowledgeBaseService knowledgeBaseService,
            MilvusVectorStore milvusVectorStore,
            ConversationService conversationService) {
        this.conversationService = conversationService;
        this.chatClient = chatClient;
        this.knowledgeBaseService = knowledgeBaseService;
        this.milvusVectorStore = milvusVectorStore;
    }

    @Override
    public Flux<String> kbChatStream(String prompt, String kbName, String chatId) {
        try {
            log.info("开始知识库问答: kbName={}, chatId={}, prompt={}", kbName, chatId, prompt);
            
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                SimpleChatResponseVo errorData = new SimpleChatResponseVo("知识库不存在: " + kbName, chatId);
                StreamResponseVo errorVo = StreamResponseVo.error("知识库不存在: " + kbName);
                try {
                    return Flux.just(objectMapper.writeValueAsString(errorVo));
                } catch (Exception e) {
                    return Flux.just("data: 知识库不存在: " + kbName + "\n\n");
                }
            }

            // 更新会话的标题，只在首次
            QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("session_id", chatId).eq("chat_type", "kb_chat");
            Conversation conversation = conversationService.getOne(queryWrapper);
            if (conversation != null) {
                updateConversationTitleIfNeeded(conversation, prompt);
            }

            // 构建集合名称
            String collectionName = "kb_" + knowledgeBase.getId();
            String filter = "collectionName == '" + collectionName + "'";
            String errorJson;
            try {
                errorJson = objectMapper.writeValueAsString(StreamResponseVo.error("知识库问答失败，请稍后重试"));
            } catch (Exception e) {
                errorJson = "data: 知识库问答失败，请稍后重试\n\n";
            }
            return chatClient
                    .prompt()
                    .user(prompt)
                    .advisors(a -> a.param(QuestionAnswerAdvisor.FILTER_EXPRESSION, filter))
                    .advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId))
                    .stream()
                    .content()
                    .map(content -> {
                        SimpleChatResponseVo data = new SimpleChatResponseVo(content, chatId);
                        StreamResponseVo vo = StreamResponseVo.success(data);
                        try {
                            return objectMapper.writeValueAsString(vo);
                        } catch (Exception e) {
                            return "data: " + content + "\n\n";
                        }
                    })
                    .concatWith(Flux.just("[DONE]"))
                    .doOnComplete(() -> log.info("知识库问答完成: kbName={}, chatId={}", kbName, chatId))
                    .doOnError(error -> log.error("知识库问答失败: kbName={}, chatId={}", kbName, chatId, error))
                    .onErrorReturn(errorJson);

        } catch (Exception e) {
            log.error("知识库问答异常: kbName={}, chatId={}", kbName, chatId, e);
            StreamResponseVo errorVo = StreamResponseVo.error("知识库问答异常: " + e.getMessage());
            try {
                return Flux.just(objectMapper.writeValueAsString(errorVo));
            } catch (Exception ex) {
                return Flux.just("data: 知识库问答异常: " + e.getMessage() + "\n\n");
            }
        }
    }

    @Override
    public String kbChat(String prompt, String kbName) {
        try {
            log.info("开始知识库问答: kbName={}, prompt={}", kbName, prompt);
            
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                return "知识库不存在: " + kbName;
            }

            // 构建集合名称
            String collectionName = "kb_" + knowledgeBase.getId();
            
            // 使用RAG进行问答
            String response = chatClient
                    .prompt()
                    .user(prompt)
                    .advisors(new QuestionAnswerAdvisor(milvusVectorStore))
                    .call()
                    .content();

            log.info("知识库问答完成: kbName={}", kbName);
            return response;

        } catch (Exception e) {
            log.error("知识库问答异常: kbName={}", kbName, e);
            return "知识库问答异常: " + e.getMessage();
        }
    }

    /**
     * 更新会话的标题，只在首次
     * @param conversation
     * @param prompt
     */
    private void updateConversationTitleIfNeeded(Conversation conversation, String prompt) {
        try {
            // 如果是第一条消息，设置标题
            if (conversation.getTitle() == null || conversation.getTitle().trim().isEmpty()) {
                String title = prompt.length() > 20 ? prompt.substring(0, 20) : prompt;
                conversation.setTitle(title);

                // 更新会话
                conversationService.updateById(conversation);

                log.debug("Updated conversation title for {}: {}", conversation.getSessionId(), title);
            }
        } catch (Exception e) {
            log.error("Failed to update conversation title for {}: {}",
                    conversation.getSessionId(), e.getMessage());
        }
    }
}
