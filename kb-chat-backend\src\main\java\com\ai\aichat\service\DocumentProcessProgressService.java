package com.ai.aichat.service;

import com.ai.aichat.websocket.DocumentProcessWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理进度服务
 */
@Slf4j
@Service
public class DocumentProcessProgressService {

    @Autowired
    private DocumentProcessWebSocketHandler webSocketHandler;

    /**
     * 发送处理开始消息
     */
    public void sendProcessStart(Long kbId, int totalFiles) {
        Map<String, Object> message = Map.of(
            "type", "process_start",
            "kbId", kbId,
            "totalFiles", totalFiles,
            "message", "开始处理文档",
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送处理开始消息: kbId={}, totalFiles={}", kbId, totalFiles);
    }

    /**
     * 发送文件处理开始消息
     */
    public void sendFileProcessStart(Long kbId, String fileName, int currentIndex, int totalFiles) {
        int progress = (int) ((double) currentIndex / totalFiles * 100);
        
        Map<String, Object> message = Map.of(
            "type", "file_process_start",
            "kbId", kbId,
            "fileName", fileName,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "开始处理文件: " + fileName,
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件处理开始消息: kbId={}, fileName={}, progress={}%", kbId, fileName, progress);
    }

    /**
     * 发送文件解析完成消息
     */
    public void sendFileParseComplete(Long kbId, String fileName, int chunkCount, int currentIndex, int totalFiles) {
        int progress = (int) ((double) (currentIndex * 0.8 + 0.3) / totalFiles * 100);
        
        Map<String, Object> message = Map.of(
            "type", "file_parse_complete",
            "kbId", kbId,
            "fileName", fileName,
            "chunkCount", chunkCount,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "文件解析完成，生成 " + chunkCount + " 个片段",
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件解析完成消息: kbId={}, fileName={}, chunkCount={}, progress={}%", 
                kbId, fileName, chunkCount, progress);
    }

    /**
     * 发送向量化开始消息
     */
    public void sendVectorizationStart(Long kbId, String fileName, int totalChunks, int currentIndex, int totalFiles) {
        int progress = (int) ((double) (currentIndex * 0.8 + 0.5) / totalFiles * 100);

        Map<String, Object> message = Map.of(
            "type", "vectorization_start",
            "kbId", kbId,
            "fileName", fileName,
            "totalChunks", totalChunks,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "开始向量化处理 " + totalChunks + " 个片段...",
            "timestamp", System.currentTimeMillis()
        );

        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送向量化开始消息: kbId={}, fileName={}, totalChunks={}, progress={}%",
                kbId, fileName, totalChunks, progress);
    }

    /**
     * 发送向量化进度消息
     */
    public void sendVectorizationProgress(Long kbId, String fileName, int processedChunks, int totalChunks,
                                        int currentIndex, int totalFiles) {
        int fileProgress = (int) ((double) processedChunks / totalChunks * 100);
        int overallProgress = (int) ((double) (currentIndex * 0.8 + 0.5 + (double) processedChunks / totalChunks * 0.3) / totalFiles * 100);

        Map<String, Object> message = new HashMap<>();
        message.put("type", "vectorization_progress");
        message.put("kbId", kbId);
        message.put("fileName", fileName);
        message.put("processedChunks", processedChunks);
        message.put("totalChunks", totalChunks);
        message.put("fileProgress", fileProgress);
        message.put("currentIndex", currentIndex);
        message.put("totalFiles", totalFiles);
        message.put("progress", overallProgress);
        message.put("message", String.format("向量化进度: %d/%d 片段 (%d%%)", processedChunks, totalChunks, fileProgress));
        message.put("timestamp", System.currentTimeMillis());

        webSocketHandler.sendProgressToKb(kbId, message);
        log.debug("发送向量化进度消息: kbId={}, fileName={}, progress={}/{} ({}%)",
                kbId, fileName, processedChunks, totalChunks, fileProgress);
    }

    /**
     * 发送文件处理完成消息
     */
    public void sendFileProcessComplete(Long kbId, String fileName, int chunkCount, int currentIndex, int totalFiles) {
        int progress = (int) ((double) (currentIndex + 1) / totalFiles * 100);
        
        Map<String, Object> message = Map.of(
            "type", "file_process_complete",
            "kbId", kbId,
            "fileName", fileName,
            "chunkCount", chunkCount,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "文件处理完成: " + fileName,
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件处理完成消息: kbId={}, fileName={}, chunkCount={}, progress={}%", 
                kbId, fileName, chunkCount, progress);
    }

    /**
     * 发送处理完成消息
     */
    public void sendProcessComplete(Long kbId, int totalFiles, int totalChunks) {
        Map<String, Object> message = Map.of(
            "type", "process_complete",
            "kbId", kbId,
            "totalFiles", totalFiles,
            "totalChunks", totalChunks,
            "progress", 100,
            "message", "文档处理完成！共处理 " + totalFiles + " 个文件，生成 " + totalChunks + " 个片段",
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送处理完成消息: kbId={}, totalFiles={}, totalChunks={}", kbId, totalFiles, totalChunks);
    }

    /**
     * 发送处理失败消息
     */
    public void sendProcessError(Long kbId, String fileName, String errorMessage, int currentIndex, int totalFiles) {
        Map<String, Object> message = Map.of(
            "type", "process_error",
            "kbId", kbId,
            "fileName", fileName,
            "errorMessage", errorMessage,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "message", "处理文件失败: " + fileName + " - " + errorMessage,
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.error("发送处理失败消息: kbId={}, fileName={}, error={}", kbId, fileName, errorMessage);
    }
}
