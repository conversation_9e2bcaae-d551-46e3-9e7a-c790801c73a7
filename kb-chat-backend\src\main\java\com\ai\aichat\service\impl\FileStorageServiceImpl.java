package com.ai.aichat.service.impl;

import com.ai.aichat.config.FileStorageConfig;
import com.ai.aichat.service.FileStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

/**
 * 文件存储服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileStorageServiceImpl implements FileStorageService {

    private final FileStorageConfig fileStorageConfig;

    @Override
    public Boolean createKnowledgeBaseDirectories(String kbName) {
        try {
            // 创建知识库主目录（文档直接存储在这里，不需要docs子目录）
            Path kbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(kbName));
            Files.createDirectories(kbPath);

            // 不再创建docs子目录和vector_db目录
            // Path docsPath = Paths.get(fileStorageConfig.getKnowledgeBaseDocsPath(kbName));
            // Files.createDirectories(docsPath);
            // Path vectorPath = Paths.get(fileStorageConfig.getKnowledgeBaseVectorPath(kbName));
            // Files.createDirectories(vectorPath);

            log.info("成功创建知识库目录结构: {}", kbName);
            return true;

        } catch (IOException e) {
            log.error("创建知识库目录失败: {}", kbName, e);
            return false;
        }
    }

    @Override
    public Boolean deleteKnowledgeBaseDirectories(String kbName) {
        try {
            // 删除知识库主目录（包含所有文档）
            Path kbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(kbName));
            if (Files.exists(kbPath)) {
                deleteDirectoryRecursively(kbPath);
            }

            // 不再需要单独删除vector_db目录
            // Path vectorPath = Paths.get(fileStorageConfig.getKnowledgeBaseVectorPath(kbName));
            // if (Files.exists(vectorPath)) {
            //     deleteDirectoryRecursively(vectorPath);
            // }

            log.info("成功删除知识库目录: {}", kbName);
            return true;

        } catch (IOException e) {
            log.error("删除知识库目录失败: {}", kbName, e);
            return false;
        }
    }

    @Override
    public Boolean renameKnowledgeBaseDirectory(String oldName, String newName) {
        try {
            // 重命名知识库主目录
            Path oldKbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(oldName));
            Path newKbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(newName));

            if (Files.exists(oldKbPath)) {
                Files.move(oldKbPath, newKbPath);
            }

            // 不再需要重命名vector_db目录
            // Path oldVectorPath = Paths.get(fileStorageConfig.getKnowledgeBaseVectorPath(oldName));
            // Path newVectorPath = Paths.get(fileStorageConfig.getKnowledgeBaseVectorPath(newName));
            // if (Files.exists(oldVectorPath)) {
            //     Files.move(oldVectorPath, newVectorPath);
            // }

            log.info("成功重命名知识库目录: {} -> {}", oldName, newName);
            return true;

        } catch (IOException e) {
            log.error("重命名知识库目录失败: {} -> {}", oldName, newName, e);
            return false;
        }
    }

    @Override
    public Boolean knowledgeBaseDirectoryExists(String kbName) {
        Path kbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(kbName));
        return Files.exists(kbPath) && Files.isDirectory(kbPath);
    }

    @Override
    public List<File> getKnowledgeBaseFiles(String kbName) {
        List<File> files = new ArrayList<>();
        try {
            // 直接从知识库主目录获取文件，不再使用docs子目录
            Path kbPath = Paths.get(fileStorageConfig.getKnowledgeBasePath(kbName));
            if (Files.exists(kbPath)) {
                try (Stream<Path> paths = Files.walk(kbPath)) {
                    paths.filter(Files::isRegularFile)
                         .forEach(path -> files.add(path.toFile()));
                }
            }
        } catch (IOException e) {
            log.error("获取知识库文件列表失败: {}", kbName, e);
        }
        return files;
    }

    @Override
    public Integer getKnowledgeBaseFileCount(String kbName) {
        return getKnowledgeBaseFiles(kbName).size();
    }

    @Override
    public Long getKnowledgeBaseSize(String kbName) {
        long totalSize = 0;
        try {
            Path docsPath = Paths.get(fileStorageConfig.getKnowledgeBaseDocsPath(kbName));
            if (Files.exists(docsPath)) {
                try (Stream<Path> paths = Files.walk(docsPath)) {
                    totalSize = paths.filter(Files::isRegularFile)
                                   .mapToLong(path -> {
                                       try {
                                           return Files.size(path);
                                       } catch (IOException e) {
                                           return 0L;
                                       }
                                   })
                                   .sum();
                }
            }
        } catch (IOException e) {
            log.error("计算知识库大小失败: {}", kbName, e);
        }
        return totalSize;
    }

    @Override
    public Boolean clearKnowledgeBaseContent(String kbName) {
        try {
            Path docsPath = Paths.get(fileStorageConfig.getKnowledgeBaseDocsPath(kbName));
            if (Files.exists(docsPath)) {
                try (Stream<Path> paths = Files.walk(docsPath)) {
                    paths.filter(Files::isRegularFile)
                         .forEach(path -> {
                             try {
                                 Files.delete(path);
                             } catch (IOException e) {
                                 log.warn("删除文件失败: {}", path, e);
                             }
                         });
                }
            }
            
            log.info("成功清空知识库内容: {}", kbName);
            return true;
            
        } catch (IOException e) {
            log.error("清空知识库内容失败: {}", kbName, e);
            return false;
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectoryRecursively(Path path) throws IOException {
        try (Stream<Path> paths = Files.walk(path)) {
            paths.sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                 .forEach(p -> {
                     try {
                         Files.delete(p);
                     } catch (IOException e) {
                         log.warn("删除文件/目录失败: {}", p, e);
                     }
                 });
        }
    }
}
