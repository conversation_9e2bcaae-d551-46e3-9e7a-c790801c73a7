package com.ai.aichat.controller;

import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.ai.aichat.service.ModelStructureService;
import com.ai.aichat.util.FileUploadUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "版本管理", description = "模型版本管理相关接口")
@RestController
@RequestMapping("/version")
public class VersionController {

    private static final Logger log = LoggerFactory.getLogger(VersionController.class);

    @Autowired
    private ModelStructureService modelStructureService;

    @Value("${app.model.upload-path:data/models}")
    private String modelUploadPath;

    @Value("${app.model.export-path:data/exports}")
    private String modelExportPath;

    /**
     * 初始化方法，确保上传和导出目录存在
     */
    @PostConstruct
    public void init() {
        try {
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");
            log.info("项目根目录: {}", projectRoot);

            // 创建上传目录（基于项目根目录）
            Path uploadPath = Paths.get(projectRoot, modelUploadPath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("创建上传目录: {}", uploadPath.toAbsolutePath());
            } else {
                log.info("上传目录已存在: {}", uploadPath.toAbsolutePath());
            }

            // 创建导出目录（基于项目根目录）
            Path exportPath = Paths.get(projectRoot, modelExportPath);
            if (!Files.exists(exportPath)) {
                Files.createDirectories(exportPath);
                log.info("创建导出目录: {}", exportPath.toAbsolutePath());
            } else {
                log.info("导出目录已存在: {}", exportPath.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("初始化目录失败", e);
        }
    }

    @Operation(summary = "更新模型信息")
    @PutMapping("/model/{id}")
    public Map<String, Object> updateModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String version = (String) request.get("version");
            String description = (String) request.get("description");
            
            log.info("更新模型信息，ID: {}, 版本: {}", id, version);
            
            Boolean result = modelStructureService.updateModelStructure(id, version, null, null, description, null);
            
            if (result) {
                response.put("success", true);
                response.put("message", "更新成功");
            } else {
                response.put("success", false);
                response.put("error", "更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新模型信息失败，ID: {}", id, e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    @Operation(summary = "删除模型")
    @DeleteMapping("/model/{id}")
    public Map<String, Object> deleteModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("删除模型，ID: {}", id);
            
            Boolean result = modelStructureService.deleteModelStructure(id);
            
            if (result) {
                response.put("success", true);
                response.put("message", "删除成功");
            } else {
                response.put("success", false);
                response.put("error", "删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除模型失败，ID: {}", id, e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    @Operation(summary = "上传模型文件夹")
    @PostMapping("/upload-model-folder")
    public Map<String, Object> uploadModelFolder(
            @RequestParam("folderName") String folderName,
            @RequestParam Map<String, MultipartFile> files) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("上传模型文件夹，文件夹名称: {}, 文件数量: {}", folderName, files.size());

            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 创建目标目录（基于项目根目录）
            String targetDir = projectRoot + File.separator + modelUploadPath + File.separator + folderName + "_" + System.currentTimeMillis();
            Path targetPath = Paths.get(targetDir);
            Files.createDirectories(targetPath);

            log.info("创建目标目录: {}", targetPath.toAbsolutePath());

            // 保存文件
            for (Map.Entry<String, MultipartFile> entry : files.entrySet()) {
                String paramName = entry.getKey();
                MultipartFile file = entry.getValue();

                if (file != null && !file.isEmpty()) {
                    // 处理参数名，提取相对路径
                    String relativePath = paramName;
                    if (paramName.startsWith("files[") && paramName.endsWith("]")) {
                        relativePath = paramName.substring(6, paramName.length() - 1);
                    }

                    // 安全检查：防止路径遍历攻击
                    if (relativePath.contains("..") || relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                        log.warn("检测到不安全的文件路径: {}", relativePath);
                        continue;
                    }

                    Path filePath = targetPath.resolve(relativePath);

                    // 确保父目录存在
                    Path parentDir = filePath.getParent();
                    if (parentDir != null && !Files.exists(parentDir)) {
                        Files.createDirectories(parentDir);
                        log.debug("创建父目录: {}", parentDir);
                    }

                    // 保存文件
                    file.transferTo(filePath.toFile());
                    log.debug("保存文件: {} -> {}", file.getOriginalFilename(), filePath);
                }
            }

            // 返回相对路径给前端
            String relativePath = modelUploadPath + "/" + folderName + "_" + System.currentTimeMillis();

            response.put("success", true);
            response.put("save_dir", relativePath);  // 返回相对路径
            response.put("message", "上传成功");

        } catch (Exception e) {
            log.error("上传模型文件夹失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return response;
    }

    @Operation(summary = "导入模型信息")
    @PostMapping("/import-model")
    public Map<String, Object> importModel(@RequestBody Map<String, Object> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String version = (String) request.get("version");
            String baseModel = (String) request.get("base_model");
            String description = (String) request.get("description");
            String modelPath = (String) request.get("model_path");
            String dataset = (String) request.get("dataset");
            
            log.info("导入模型信息，版本: {}, 基础模型: {}", version, baseModel);
            
            if (version == null || version.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "版本不能为空");
                return response;
            }
            
            if (baseModel == null || baseModel.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "基础模型不能为空");
                return response;
            }
            
            Boolean result = modelStructureService.createModelStructure(
                version.trim(), 
                baseModel.trim(), 
                dataset, 
                description, 
                modelPath
            );
            
            if (result) {
                response.put("success", true);
                response.put("message", "导入成功");
            } else {
                response.put("success", false);
                response.put("error", "导入失败");
            }
            
        } catch (Exception e) {
            log.error("导入模型信息失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    @Operation(summary = "获取导出路径")
    @GetMapping("/export-path")
    public Map<String, Object> getExportPath() {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 确保导出目录存在（基于项目根目录）
            Path exportPath = Paths.get(projectRoot, modelExportPath);
            if (!Files.exists(exportPath)) {
                Files.createDirectories(exportPath);
            }

            response.put("export_path", exportPath.toAbsolutePath().toString());
            response.put("success", true);
            
        } catch (Exception e) {
            log.error("获取导出路径失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("export_path", modelExportPath); // 即使失败也返回路径
        }
        
        return response;
    }

    @Operation(summary = "导出模型")
    @PostMapping("/export-model/{id}")
    public Map<String, Object> exportModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("导出模型，ID: {}", id);
            
            // 获取模型信息
            ModelStructureVo model = modelStructureService.getModelStructureById(id);
            if (model == null) {
                response.put("success", false);
                response.put("error", "模型不存在");
                return response;
            }
            
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 创建导出目录（基于项目根目录）
            String exportDir = projectRoot + File.separator + modelExportPath + File.separator + "model_" + model.getVersion() + "_" + System.currentTimeMillis();
            Path exportPath = Paths.get(exportDir);
            Files.createDirectories(exportPath);
            
            // 这里可以添加实际的模型文件复制逻辑
            // 目前只是创建一个信息文件
            String modelInfo = String.format(
                "模型版本: %s\n基础模型: %s\n描述: %s\n模型路径: %s\n数据集: %s\n",
                model.getVersion(),
                model.getBase_model(),
                model.getDescription(),
                model.getModel_path(),
                model.getDataset()
            );
            
            Files.write(exportPath.resolve("model_info.txt"), modelInfo.getBytes());
            
            response.put("success", true);
            response.put("export_path", exportDir);
            response.put("message", "导出成功");
            
        } catch (Exception e) {
            log.error("导出模型失败，ID: {}", id, e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    @Operation(summary = "获取模型详情")
    @GetMapping("/model/{id}")
    public Map<String, Object> getModelDetail(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("获取模型详情，ID: {}", id);
            
            ModelStructureVo model = modelStructureService.getModelStructureById(id);
            
            response.put("success", true);
            response.put("data", model);
            
        } catch (Exception e) {
            log.error("获取模型详情失败，ID: {}", id, e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
}
