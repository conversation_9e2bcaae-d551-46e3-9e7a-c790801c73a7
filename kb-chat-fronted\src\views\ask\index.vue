<template>
  <div class="container">
    <audio-recorder
      ref="audioRecorder"
      @recording-complete="handleRecordingComplete"
    />
    
    <div class="title">知识问答助手</div>

    <!-- 主内容区域，采用类似ChatGPT的布局 -->
    <div class="main-content">
      <!-- 可收缩侧边栏 -->
      <div class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-content" v-if="!sidebarCollapsed">
          <!-- 将知识库按钮放在新建对话按钮上方 -->
          <div class="sidebar-btns">
            <div class="utility-btn" @click="goKnowledge">
              <el-icon><FolderAdd /></el-icon>
              <span>知识库构建</span>
            </div>
            <div
              class="utility-btn"
              @click="saveChat"
              :class="{ disabled: !chatWindow?.talkInfo?.length }">
              <el-icon><Document /></el-icon>
              <span>保存问答</span>
            </div>
          </div>
          
          <div class="divider"></div>
          
          <!-- 新增新建对话按钮 -->
          <div class="new-chat-btn" @click="newChat">
            <el-icon><Document /></el-icon>
            <span>新建对话</span>
          </div>
          
          <!-- 使用历史聊天记录组件 -->
          <chat-history 
            ref="chatHistoryRef"
            :current-session-id="currentSessionId" 
            @session-loaded="handleSessionLoaded"
            @session-deleted="handleSessionDeleted"
            @loading-history-start="handleLoadingHistoryStart"
          />
        </div>
      </div>

      <!-- 聊天主界面 -->
      <main :class="{ 'sidebar-expanded': !sidebarCollapsed, 'preview-active': isFilePreviewOpen }">
        <!-- 设置按钮 -->
        <div class="top-control-buttons">
          <div class="settings-btn" @click="toggleSidebar" title="参数设置">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="new-chat-btn-top" @click="newChat" title="新建对话">
            <el-icon><Document /></el-icon>
          </div>
        </div>
        
        <div class="top-actions">
          <div class="close-btn" @click="close">
            <el-icon><Close /></el-icon>
            <span>关闭对话</span>
          </div>
        </div>
        
        <!-- 将对话类型选择移到这里（输入框上方） -->
        <div class="chat-type-selector">
          <div class="current-model-info clickable" @click="openModelSettingsDialog">
            <el-icon><Cpu /></el-icon>
            <span class="model-name" :class="{'loading-text': isLoadingCurrentModel}">
              {{ currentModelName || "未知模型" }}
              <el-icon v-if="isLoadingCurrentModel" class="is-loading"><Loading /></el-icon>
            </span>
            <el-icon class="settings-icon"><Setting /></el-icon>
          </div>
          <select-d-bdialog v-model="selectedkb" />
        </div>
        
        <chat-window
          ref="chatWindow"
          :selected-kb="selectedkb"
          :session-id="currentSessionId"
          @preview-change="handleFilePreviewChange"
          @session-created="handleSessionCreated"
        />

        <!-- 底部输入区域 -->
        <div class="input-area" :class="{ 'preview-active': isFilePreviewOpen }">
          <!-- 添加问候语 -->
          <div class="greeting-text" v-if="!chatWindow?.talkInfo?.length && isNewSession">
            <div class="greeting-main">{{ getGreeting() }}，很高兴见到你！</div>
            <div class="greeting-sub">我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~</div>
          </div>
          <div class="input-container">
            <div class="record-btn" @click="record" title="语音输入">
              <el-icon><Microphone /></el-icon>
            </div>
            <div class="input-wrapper">
              <textarea 
                v-model="inputValue" 
                @keydown.enter="handleEnter"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                @input="autoAdjustHeight"
                ref="textareaInput"
                :placeholder="getGreeting() + '，很高兴见到你！'"
                class="chat-input"
                rows="1"
              ></textarea>
            </div>
            <!-- 中止按钮 -->
            <div
              v-if="chatWindow?.loading && !chatWindow?.isAborting"
              class="abort-btn"
              @click="abortResponse"
              title="中止回答">
              <el-icon><Close /></el-icon>
            </div>
            <!-- 发送按钮 -->
            <div
              v-else
              class="send-btn"
              :class="{ 'loading': chatWindow?.loading }"
              @click="submit"
              title="发送">
              <el-icon><Promotion /></el-icon>
            </div>
          </div>

        </div>
      </main>
    </div>

    <!-- 保存问答对话框 -->
    <el-dialog
      v-model="saveDialogVisible"
      title="保存问答对"
      width="500px"
      :show-close="true"
      :close-on-click-modal="false"
      destroy-on-close
      class="save-dialog"
      center
    >
      <div class="save-form">
        <div class="form-item">
          <span class="label">问答库：</span>
          <el-select
            v-model="saveFormData.kb_id"
            placeholder="请选择问答库"
            class="kb-select"
            popper-class="kb-select-dropdown">
            <el-option
              v-for="item in saveFormData.kbList"
              :key="item.kb_id"
              :label="item.kb_name"
              :value="item.kb_id">
              <div class="kb-option">
                <span class="kb-name" :title="item.kb_name">{{ item.kb_name }}</span>
                <span class="kb-description" v-if="item.description">{{ item.description }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
        
        <div class="form-item">
          <span class="label">数据集名称：</span>
          <el-input
            v-model="saveFormData.datasetName"
            placeholder="请输入数据集名称"
            maxlength="50"
            show-word-limit />
        </div>
        
        <!-- 问答对选择列表 -->
        <div class="form-item qa-pairs-selection">
          <div class="qa-selection-header">
            <span class="label">选择要保存的问答对：</span>
            <el-checkbox 
              v-model="saveFormData.selectAll" 
              @change="handleSelectAllChange"
              :indeterminate="isIndeterminate">
              {{ saveFormData.selectAll ? '取消全选' : '全选' }}
            </el-checkbox>
          </div>
          
          <div class="qa-pairs-list">
            <el-checkbox-group v-model="saveFormData.selectedPairs" @change="handleSelectedChange">
              <div v-for="(pair, index) in saveFormData.qaPairs" :key="index" class="qa-pair-item">
                <el-checkbox :label="index">
                  <div class="qa-pair-content">
                    <div class="qa-question">问：{{ pair.question }}</div>
                    <div class="qa-answer">答：{{ formatAnswer(pair.answer) }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
            
            <div v-if="saveFormData.qaPairs.length === 0" class="no-qa-pairs">
              没有可保存的问答对
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSave" :disabled="!saveFormData.datasetName || !saveFormData.selectedPairs.length">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加模型设置对话框 -->
    <el-dialog
      v-model="modelSettingsDialogVisible"
      title="模型设置"
      width="450px"
      :show-close="true"
      :close-on-click-modal="false"
      destroy-on-close
      class="model-settings-dialog"
      center
    >
      <div class="model-settings-content">
        <!-- 模型选择下拉框 -->
        <div class="model-select-section">
          <div class="settings-label">选择模型</div>
          <div v-if="isLoadingModelData" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载模型列表...</span>
          </div>
          <el-select
            v-else
            v-model="selectedModel"
            placeholder="请选择模型"
            @change="handleModelChange"
            class="model-select"
            popper-class="model-select-dropdown">
            <template #prefix>
              <el-icon><Cpu /></el-icon>
            </template>
            <el-option
              v-for="model in modelList"
              :key="model.path"
              :label="model.name"
              :value="model.path">
              <div class="model-option">
                <span class="model-name" :title="model.name">{{ model.name }}</span>
              </div>
            </el-option>
          </el-select>
        </div>

        <!-- 模型参数设置 -->
        <div class="model-params-section">
          <div class="settings-label">生成设置</div>
          
          <div v-if="isLoadingModelData" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载模型配置...</span>
          </div>
          <template v-else>
          <div class="param-item">
            <div class="param-header">
              <span class="param-label">
                最大生成长度
                <el-tooltip effect="dark" placement="right">
                  <template #content>
                    控制模型每次回答最多可以生成多少个字符。较大的值支持更长的回答，但会消耗更多资源和时间。
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </span>
              <el-input-number 
                v-model="modelConfig.max_new_tokens"
                :min="64"
                :max="8192"
                :step="64"
                size="small"
                class="param-input"
              />
            </div>
            <el-slider
              v-model="modelConfig.max_new_tokens"
              :min="64"
              :max="8192"
              :step="64"
              :marks="{
                64: '64',
                1024: '1K',
                2048: '2K',
                4096: '4K',
                8192: '8K'
              }"
              class="param-slider">
            </el-slider>
          </div>
          
          <div class="param-item">
            <div class="param-header">
              <span class="param-label">
                温度
                <el-tooltip effect="dark" placement="right">
                  <template #content>
                    控制文本生成的随机性。值越高(0-2)，生成的内容越具有创造性但可能不太可控；值越低，生成的内容更加确定和保守。
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </span>
              <el-input-number 
                v-model="modelConfig.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                :precision="1"
                size="small"
                class="param-input"
              />
            </div>
            <el-slider
              v-model="modelConfig.temperature"
              :min="0"
              :max="2"
              :step="0.1">
            </el-slider>
          </div>
          
          <div class="param-item">
            <div class="param-header">
              <span class="param-label">
                Top P
                <el-tooltip effect="dark" placement="right">
                  <template #content>
                    控制文本生成的采样范围。值越高(0-1)，生成的内容更多样；值越低，生成的内容更加聚焦和确定。
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </span>
              <el-input-number 
                v-model="modelConfig.top_p"
                :min="0"
                :max="1"
                :step="0.1"
                :precision="1"
                size="small"
              />
            </div>
            <el-slider
              v-model="modelConfig.top_p"
              :min="0"
              :max="1"
              :step="0.1">
            </el-slider>
          </div>
          </template>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modelSettingsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyModelSettings" :loading="applyingSettings">
            {{ applyingSettings ? '应用中...' : '应用设置' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, h, watch, computed, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useDialogStore } from "/src/store/dialog.js";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import SelectDBdialog from "./components/selectDBdialog.vue";
import AudioRecorder from './components/AudioRecorder.vue';
import ChatWindow from './components/ChatWindow.vue';
import { Setting, Close, Microphone, Promotion, Document, Plus, Cpu, Loading, QuestionFilled, FolderAdd } from '@element-plus/icons-vue'
import { transcribeAudio } from '@/api/knowledgeManage/audio';
import ChatHistory from './components/ChatHistory.vue';

const dialogStore = useDialogStore();
let { selectedkb, AskPushToKnowledge, currentModelName } = storeToRefs(dialogStore);
const router = useRouter();
const audioRecorder = ref(null);
const chatWindow = ref(null);
const sidebarCollapsed = ref(false);
const inputValue = ref('');
const isFilePreviewOpen = ref(false);
const textareaInput = ref(null);
const isComposing = ref(false);
const saveDialogVisible = ref(false);
const saveFormData = ref({
  datasetName: '',
  kb_id: '',        // 改为 kb_id
  kbList: [],
  qaPairs: [], // 存储所有问答对
  selectedPairs: [], // 存储选中的问答对索引
  selectAll: false // 是否全选
});
const modelSettingsDialogVisible = ref(false);
const selectedModel = ref('');
const modelList = ref([]);
const modelConfig = reactive({
  max_new_tokens: 1024,
  temperature: 0.8,
  top_p: 0.9
});
const applyingSettings = ref(false);
const isLoadingModelData = ref(false);
const isLoadingCurrentModel = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API || '/dev-api';
const currentSessionId = ref(null);
const chatHistoryRef = ref(null);
const isLoadingHistory = ref(false);
const loadingHistoryId = ref(null);
const isNewSession = ref(true); // 标记是否为新会话，默认为true

// 确保必要的状态引用正确初始化
try {
  // 如果获取状态失败，使用本地状态
  if (!selectedkb) selectedkb = ref("");
  if (!AskPushToKnowledge) AskPushToKnowledge = ref(false);
  if (!currentModelName) currentModelName = ref(""); // 初始为空，等待加载
  
  console.log('States initialized:', {
    selectedkb: selectedkb.value,
    AskPushToKnowledge: AskPushToKnowledge.value,
    currentModelName: currentModelName.value
  });
} catch (error) {
  console.error('Error initializing states:', error);
  // 重新初始化本地状态
  selectedkb = ref("");
  AskPushToKnowledge = ref(false);
  currentModelName = ref(""); // 初始为空，等待加载
}

// 切换侧边栏可见性
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  // 保存到localStorage
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value ? 'true' : 'false');
};

const record = () => {
  audioRecorder.value.startRecord();
};

const handleRecordingComplete = async (audioBlob) => {
  try {
    await sentRecord(audioBlob);
  } catch (error) {
    ElMessage.error("处理录音失败：" + error.message);
  }
};

const sentRecord = async (audioBlob) => {
  try {
    const formData = new FormData();
    const filename = `recording_${Date.now()}.wav`;
    formData.append(
      "file",
      new File([audioBlob], filename, { type: "audio/wav" })
    );

    const response = await transcribeAudio(formData);

    if (response.code !== 0) {
      throw new Error(response.msg || "音频转录失败");
    }

    inputValue.value = response.data.transcription;
    ElMessage.success(response.msg || "音频转录成功");
  } catch (error) {
    ElMessage.error(error.message || "音频转录失败");
    console.error("音频转录失败:", error);
  }
};

// 计算属性，表示是否为部分选中状态
const isIndeterminate = computed(() => {
  return saveFormData.value.selectedPairs.length > 0 && 
         saveFormData.value.selectedPairs.length < saveFormData.value.qaPairs.length;
});

// 处理全选/取消全选
const handleSelectAllChange = (val) => {
  saveFormData.value.selectedPairs = val ? 
    saveFormData.value.qaPairs.map((_, index) => index) : [];
};

// 处理选中状态变化
const handleSelectedChange = (value) => {
  const checkedCount = value.length;
  saveFormData.value.selectAll = checkedCount === saveFormData.value.qaPairs.length;
};

// 格式化答案，截取前60个字符
const formatAnswer = (answer) => {
  if (answer.length > 60) {
    return answer.substring(0, 60) + '...';
  }
  return answer;
};

const saveChat = async () => {
  try {
    if (!chatWindow.value.talkInfo.length) {
      ElMessage.warning("没有可保存的对话内容！");
      return;
    }

    // 获取问答库列表
    const response = await fetch("/dev-api/list_qa");
    const result = await response.json();
    
    if (result.code !== 0) {
      throw new Error(result.msg || "获取问答库列表失败");
    }

    // 提取问答对
    const qaPairs = [];
    for (let i = 0; i < chatWindow.value.talkInfo.length; i += 2) {
      if (i + 1 < chatWindow.value.talkInfo.length) {
        const userMessage = chatWindow.value.talkInfo[i];
        const aiMessage = chatWindow.value.talkInfo[i + 1];

        if (userMessage.type === "user" && aiMessage.type === "ai") {
          qaPairs.push({
            question: userMessage.info,
            answer: aiMessage.info,
            index: i / 2
          });
        }
      }
    }

    // 设置默认数据集名称
    saveFormData.value = {
      datasetName: `chat_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, 19)}`,
      kb_id: '', // 使用 kb_id
      kbList: result.data.kbList.map(kb => ({
        kb_id: kb.kbName,           // 使用 kbName 作为 kb_id
        kb_name: kb.kbName,
        description: kb.description || "",
        has_qa_files: kb.hasQaFiles,
        latest_file_date: kb.latestFileDate,
        qa_files: kb.qaFiles
      })),
      qaPairs: qaPairs,
      selectedPairs: qaPairs.map((_, index) => index), // 默认全选
      selectAll: true
    };
    
    // 显示对话框
    saveDialogVisible.value = true;
  } catch (error) {
    ElMessage.error(error.message || "保存对话失败");
    console.error("保存对话失败:", error);
  }
};

const confirmSave = async () => {
  if (!saveFormData.value.datasetName.trim()) {
    ElMessage.warning('数据集名称不能为空！');
    return;
  }

  if (!saveFormData.value.kb_id) {
    ElMessage.warning('请选择问答库！');
    return;
  }
  
  if (saveFormData.value.selectedPairs.length === 0) {
    ElMessage.warning('请至少选择一个问答对！');
    return;
  }
  
  try {
    // 构建选中的对话列表
    const dialogList = [];
    
    // 遍历选中的索引，获取对应的问答对
    saveFormData.value.selectedPairs.forEach(index => {
      const pair = saveFormData.value.qaPairs[index];
      if (pair) {
        dialogList.push({
          instruction: pair.question,
          input: "",
          output: pair.answer
        });
      }
    });

    // 保存到知识库
    const response = await fetch("/dev-api/save_chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        content: JSON.stringify(dialogList, null, 2),
        kbName: saveFormData.value.kb_id,
        datasetName: saveFormData.value.datasetName.trim()
      }),
    });

    const result = await response.json();

    if (result.code === 1) {
      ElMessage.success("问答对已保存到知识库");
      saveDialogVisible.value = false;
    } else {
      throw new Error(result.msg || "保存失败");
    }
  } catch (error) {
    ElMessage.error(error.message || "保存对话失败");
    console.error("保存对话失败:", error);
  }
};

const close = () => {
  router.push("/");
};

const goKnowledge = () => {
  AskPushToKnowledge.value = true;
  router.push("/knowledgeManage");
};

const submit = () => {
  if (chatWindow.value) {
    if (inputValue.value.trim()) {
      // 检查是否是新会话的第一条消息
      const isFirstMessage = isNewSession.value;

      // 提交内容后，不再是新会话
      isNewSession.value = false;

      chatWindow.value.inputValue = inputValue.value;
      chatWindow.value.sessionId = currentSessionId.value;
      chatWindow.value.handleSubmit();
      inputValue.value = '';

      // 如果是新会话的第一条消息，在发送后刷新历史记录
      if (isFirstMessage && chatHistoryRef.value) {
        // 延迟刷新，确保服务器有时间保存会话
        setTimeout(() => {
          chatHistoryRef.value.loadChatHistory(1);
        }, 500);
      }

      // 在清空内容后重置文本框高度
      nextTick(() => {
        if (textareaInput.value) {
          textareaInput.value.style.height = '40px'; // 重置为初始最小高度
          textareaInput.value.style.overflowY = 'hidden';
        }
      });
    }
  }
}

// 中止回答
const abortResponse = () => {
  if (chatWindow.value && chatWindow.value.abortResponse) {
    chatWindow.value.abortResponse();
  }
}

const handleFilePreviewChange = (isOpen) => {
  isFilePreviewOpen.value = isOpen;
};

// 处理新会话创建事件
const handleSessionCreated = (sessionId) => {
  console.log('新会话已创建，ID:', sessionId);
  
  // 如果id不存在，直接返回
  if (!sessionId) return;
  
  // 将会话ID保存到localStorage
  localStorage.setItem('currentSessionId', sessionId);
  
  // 只有在真正切换会话ID时才更新
  if (sessionId !== currentSessionId.value) {
    // 更新当前会话ID
    currentSessionId.value = sessionId;
    
    // 仅当切换会话时才刷新历史记录列表
    if (chatHistoryRef.value) {
      chatHistoryRef.value.loadChatHistory(1);
    }
  }
  // 在同一会话中发送新消息时不刷新历史
};

// 处理中文输入法开始
const handleCompositionStart = () => {
  isComposing.value = true;
};

// 处理中文输入法结束
const handleCompositionEnd = () => {
  isComposing.value = false;
};

// 处理回车键
const handleEnter = (e) => {
  // 如果正在使用输入法，不处理回车键
  if (isComposing.value) {
    return;
  }
  
  // 检查是否按住了Shift键
  if (e.shiftKey) {
    // Shift+Enter 添加换行
    return;
  }
  
  // 普通的Enter键提交
  e.preventDefault();
  submit();
};

// 自动调整文本框高度
const autoAdjustHeight = () => {
  const textarea = textareaInput.value;
  if (!textarea) return;
  
  // 重置高度，以便能够正确计算
  textarea.style.height = 'auto';
  
  // 设置新高度（行高约为23px，加上上下内边距）
  const minHeight = 40; // 单行的高度
  const maxHeight = 150; // 最大高度

  // 计算内容需要的高度
  const scrollHeight = textarea.scrollHeight;
  
  if (scrollHeight < minHeight) {
    textarea.style.height = minHeight + 'px';
  } else if (scrollHeight > maxHeight) {
    textarea.style.height = maxHeight + 'px';
    textarea.style.overflowY = 'auto';
  } else {
    textarea.style.height = scrollHeight + 'px';
    textarea.style.overflowY = 'hidden';
  }
};

const getGreeting = () => {
  const now = new Date();
  const hours = now.getHours();
  if (hours < 12) {
    return "早上好";
  } else if (hours < 18) {
    return "下午好";
  } else {
    return "晚上好";
  }
};

const newChat = () => {
  // 复用初始化新对话的逻辑
  initializeNewChat();

  ElMessage.success('已创建新对话');
};

// 添加一个新的异步数据加载方法
const loadModelData = async () => {
  if (isLoadingModelData.value) return;
  
  try {
    isLoadingModelData.value = true;
    
    // 并行请求模型列表和配置
    const [modelListResponse, configResponse] = await Promise.all([
      fetch(`${baseUrl}/model/list`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }),
      fetch(`${baseUrl}/model/config`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
    ]);
    
    // 处理模型列表
    const modelListResult = await modelListResponse.json();
    if (modelListResult.code === 1) {
      modelList.value = modelListResult.data;
      
      // 设置当前选中的模型
      const currentModel = modelList.value.find(model => model.is_current);
      if (currentModel) {
        selectedModel.value = currentModel.path;
      } else if (modelList.value.length > 0) {
        selectedModel.value = modelList.value[0].path;
      }
    }
    
    // 处理模型配置
    const configResult = await configResponse.json();
    if (configResult.code === 1) {
      const config = configResult.data;
      // 更新模型配置
      modelConfig.max_new_tokens = config.max_new_tokens || 1024;
      modelConfig.temperature = config.temperature || 0.8;
      modelConfig.top_p = config.top_p || 0.9;
    }
  } catch (error) {
    console.error('加载模型数据错误:', error);
    ElMessage.error('加载模型数据失败: ' + (error.message || '未知错误'));
  } finally {
    isLoadingModelData.value = false;
  }
};

// 获取当前已加载的模型信息
const loadCurrentModel = async () => {
  try {
    isLoadingCurrentModel.value = true;
    
    // 获取模型列表
    const response = await fetch(`${baseUrl}/model/list`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const res = await response.json();
    
    if (res.code === 1 && res.data && res.data.length > 0) {
      // 查找当前使用的模型
      const currentModel = res.data.find(model => model.is_current);
      if (currentModel) {
        // console.log('当前加载的模型:', currentModel.name);
        // 更新Pinia存储的当前模型名称
        if (currentModelName && typeof currentModelName === 'object') {
          currentModelName.value = currentModel.name;
        }
      } else if (res.data.length > 0) {
        // 如果没有标记当前模型，使用列表中的第一个
        // console.log('使用列表中第一个模型:', res.data[0].name);
        if (currentModelName && typeof currentModelName === 'object') {
          currentModelName.value = res.data[0].name;
        }
      }
    } else {
      console.warn('获取模型列表失败或列表为空');
      // 设置默认值
      if (currentModelName && typeof currentModelName === 'object') {
        currentModelName.value = "未知模型";
      }
    }
  } catch (error) {
    console.error('加载当前模型信息错误:', error);
    // 出错时设置默认值
    if (currentModelName && typeof currentModelName === 'object') {
      currentModelName.value = "未知模型";
    }
  } finally {
    isLoadingCurrentModel.value = false;
  }
};

// 修改打开对话框方法
const openModelSettingsDialog = () => {
  // 立即打开对话框
  modelSettingsDialogVisible.value = true;
  
  // 然后异步加载数据
  loadModelData();
};

const handleModelChange = (modelPath) => {
  // 模型更改时更新当前模型名称显示
  const model = modelList.value.find(m => m.path === modelPath);
  if (model && currentModelName && typeof currentModelName === 'object') {
    // 仅更新UI显示，不应用到后端直到用户点击"应用设置"
    // console.log('Selected model:', model.name);
  }
};

const applyModelSettings = async () => {
  if (!selectedModel.value) {
    ElMessage.warning('请先选择模型');
    return;
  }
  
  applyingSettings.value = true;
  // 添加模型状态提示
  const loadingMessageKey = 'model-loading';
  try {
    // 更新模型配置
    const configResponse = await fetch(`${baseUrl}/model/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(modelConfig)
    });
    
    const configRes = await configResponse.json();
    
    if (configRes.code !== 0) {
      throw new Error(configRes.msg || '更新模型配置失败');
    }
    
    // 重新加载模型
    const reloadResponse = await fetch(`${baseUrl}/model/reload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model_path: selectedModel.value
      })
    });
    
    const reloadRes = await reloadResponse.json();
    
    if (reloadRes.code !== 0) {
      throw new Error(reloadRes.msg || '重新加载模型失败');
    }
    
    // 更新当前模型名称
    const model = modelList.value.find(m => m.path === selectedModel.value);
    if (model && currentModelName && typeof currentModelName === 'object') {
      currentModelName.value = model.name;
    }
    
    // 关闭设置对话框
    modelSettingsDialogVisible.value = false;
    // 新增：提示模型加载成功
    ElMessage.success('模型加载成功');
  } catch (error) {
    console.error('应用模型设置错误:', error);
    ElMessage.error('应用模型设置失败：' + (error.message || '未知错误'));
    // 关闭加载提示
    ElMessage.close(loadingMessageKey);
  } finally {
    applyingSettings.value = false;
  }
};

const handleSessionLoaded = (data, sessionId, sessionInfo) => {
  // console.log('会话加载完成:', data, sessionId, sessionInfo);
  
  // 创建唯一的加载ID以跟踪此次加载过程
  const loadId = Date.now();
  const thisLoadId = loadId;
  loadingHistoryId.value = loadId;
  
  // 由于加载历史会话，设置标记
  isNewSession.value = false;
  
  // 先立即清空会话
  if (chatWindow.value) {
    chatWindow.value.talkInfo = [];
  }
  
  // 如果有知识库信息，设置当前知识库
  if (sessionInfo && sessionInfo.kbName) {
    // console.log('设置知识库:', sessionInfo.kbName);
    selectedkb.value = sessionInfo.kbName;
  } else if (sessionInfo && sessionInfo.type !== 'kb') {
    // 如果不是知识库对话，清空知识库选择
    // console.log('非知识库对话，清空知识库选择');
    selectedkb.value = "";
  }
  
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    try {
      // 强制重新渲染
      if (chatWindow.value && chatWindow.value.forceRender) {
        chatWindow.value.forceRender();
      }
      
      // 使用setTimeout确保React状态更新和DOM更新已完成
      setTimeout(() => {
        try {
          // 如果加载ID已变，说明用户已点击其他会话，不处理本次结果
          if (loadingHistoryId.value !== thisLoadId) {
            // console.log('加载ID不匹配，忽略此次结果');
            return;
          }
          
          // 会话加载完成，重置loading状态
          isLoadingHistory.value = false;
          loadingHistoryId.value = null;
          
          if (data && data.length > 0) {
            // 使用计算出的格式化数据填充聊天窗口
            currentSessionId.value = sessionId;
            localStorage.setItem('currentSessionId', sessionId);
            
            // 设置会话数据 - 确保正确格式化
            const formattedData = formatHistoryData(data);
            // console.log('格式化后的会话数据:', formattedData.length, '条消息');
            
            if (formattedData.length > 0) {
              chatWindow.value.talkInfo = formattedData;
              
              // 再次强制渲染
              if (chatWindow.value.forceRender) {
                chatWindow.value.forceRender();
              }
              
              // 滚动到底部
              setTimeout(() => {
                if (chatWindow.value) {
                  chatWindow.value.scrollToBottom();
                }
              }, 100);
            } else {
              console.error('格式化后的数据为空');
              ElMessage.warning('历史记录格式化失败');
            }
          } else {
            ElMessage.warning('历史记录为空');
          }
        } catch (error) {
          console.error('处理会话数据错误:', error);
          ElMessage.error('加载会话失败: ' + error.message);
          isLoadingHistory.value = false;
        }
      }, 50);
    } catch (error) {
      console.error('处理DOM更新错误:', error);
      isLoadingHistory.value = false;
    }
  });
};

const handleSessionDeleted = (sessionId) => {
  // 如果删除的是当前会话，清空聊天窗口
  if (sessionId === currentSessionId.value) {
    chatWindow.value.talkInfo = [];
    currentSessionId.value = null;
    localStorage.removeItem('currentSessionId');
  }
};

// 生成唯一的会话ID - 只在确实需要创建新会话时才调用
const generateSessionId = () => {
  const timestamp = new Date().getTime();
  const randomStr = Math.random().toString(36).substring(2, 10);
  return `${timestamp}-${randomStr}`;
};

const handleLoadingHistoryStart = (sessionId) => {
  // console.log('开始加载历史记录，会话ID:', sessionId);
  isLoadingHistory.value = true;
  
  // 为防止长时间加载，设置一个超时自动清除
  setTimeout(() => {
    if (isLoadingHistory.value) {
      isLoadingHistory.value = false;
      console.log('加载历史记录超时，自动重置状态');
    }
  }, 10000);
};

// 格式化历史数据
const formatHistoryData = (messages) => {
  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    console.warn('历史数据为空或格式不正确');
    return [];
  }
  
  // console.log('原始消息数据:', messages.length, '条消息');
  
  // 创建唯一ID前缀
  const idPrefix = `history-${Date.now()}-`;
  
  // 确保所有消息都正确格式化
  return messages.map((msg, index) => {
    // 确保消息对象有效
    if (!msg) {
      console.error('消息对象为空');
      return null;
    }
    
    try {
      // 处理graph_data: 如果是字符串则保持，如果是对象则转为字符串
      let graphData = msg.graph_data;
      if (graphData && typeof graphData === 'object') {
        graphData = JSON.stringify(graphData);
      }

      // 处理qa_pair_data: 如果是字符串则保持，如果是对象则转为字符串
      let qaPairData = msg.qa_pair_data;
      if (qaPairData && typeof qaPairData === 'object') {
        qaPairData = JSON.stringify(qaPairData);
      }

      return {
        id: `${idPrefix}${index}`, // 使用时间戳和索引创建唯一ID
        type: msg.role === 'assistant' ? 'ai' : 'user',
        info: msg.content || '',
        sources: msg.sources || [],
        graph_data: graphData, // 添加处理后的图谱数据
        qa_pair_data: qaPairData, // 添加处理后的问答对数据
        timestamp: msg.timestamp || new Date().toISOString(),
        isTyping: false,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: false
      };
    } catch (error) {
      console.error('格式化消息出错:', error, msg);
      return null;
    }
  }).filter(item => item !== null); // 过滤掉无效的消息
};

onMounted(() => {
  // 确保每次打开都是新建对话状态
  initializeNewChat();

  // 从localStorage获取侧边栏折叠状态
  const savedSidebarState = localStorage.getItem('sidebarCollapsed');
  if (savedSidebarState !== null) {
    sidebarCollapsed.value = savedSidebarState === 'true';
  }

  // 初始化文本框高度
  nextTick(() => {
    if (textareaInput.value) {
      autoAdjustHeight();
    }
  });

  // 加载当前模型信息
  loadCurrentModel();
});

// 初始化新对话状态
const initializeNewChat = () => {
  // 清空知识库选择
  selectedkb.value = "";

  // 清空输入框
  inputValue.value = '';

  // 标记为新会话
  isNewSession.value = true;

  // 生成新的会话ID
  currentSessionId.value = generateSessionId();
  localStorage.setItem('currentSessionId', currentSessionId.value);

  // 清空聊天记录
  if (chatWindow.value) {
    chatWindow.value.talkInfo = [];
  }

  // 刷新历史记录列表（显示最新状态）
  if (chatHistoryRef.value) {
    chatHistoryRef.value.loadChatHistory(1);
  }

  console.log('已初始化为新对话状态，会话ID:', currentSessionId.value);
};

onBeforeUnmount(() => {
  AskPushToKnowledge.value = false;

  // 清理状态，确保下次进入时是全新状态
  selectedkb.value = "";
  inputValue.value = '';
  isNewSession.value = true;

  // 清理localStorage中的会话ID，确保下次是新对话
  localStorage.removeItem('currentSessionId');
});
</script>

<style scoped>
.container {
  position: absolute;
  width: 100vw;
  height: 100vh;
  top: 0;
}

.title {
  width: 100%;
  height: 120px;
  margin: 0 auto;
  text-align: center;
  color: #fff;
  font-size: 50px;
  font-family: YouShe, serif;
  background-image: url("/assets/images/nav-bar-bg.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 50; /* 保持高层级，确保在所有元素之上 */
}

.main-content {
  display: flex;
  width: 100%;
  height: calc(100vh - 120px);
  position: relative;
}

/* 侧边栏样式 */
.sidebar {
  position: absolute;
  top: 0;
  height: 100%;
  width: 260px;
  min-width: 260px;
  background: linear-gradient(to bottom, rgba(9, 39, 98, 0.9), rgba(12, 30, 66, 0.9));
  border-right: 1px solid rgba(63, 99, 168, 0.5);
  transition: width 0.3s, min-width 0.3s;
  z-index: 10;
  box-shadow: 2px 0 15px 0 rgba(0, 0, 0, 0.4);
}

.sidebar.collapsed {
  width: 0;
  min-width: 0;
  overflow: hidden;
  border-right: none; /* 隐藏收缩状态下的边框 */
  box-shadow: none; /* 移除阴影效果 */
}

.sidebar-content {
  height: 100%;
  width: 100%;
  overflow: hidden; /* 改为hidden，让子元素控制滚动 */
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
}

/* 分隔线样式 */
.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(63, 99, 168, 0.5), transparent);
  margin: 15px 0;
}

.sidebar-btns {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  margin-bottom: 15px;
}

.utility-btn {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 34px;
  border-radius: 8px;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  color: #d0d9f0;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s;
  gap: 8px;
  max-width: 180px;
  margin: 0 auto;
  width: 100%;
}

.utility-btn:hover {
  background-color: rgba(15, 45, 90, 0.8);
  border-color: #13fff3;
  color: #13fff3;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(19, 255, 243, 0.2);
}

.utility-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.utility-btn .el-icon {
  font-size: 16px;
  color: #13fff3;
}

/* 新增新建对话按钮样式 */
.new-chat-btn {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(19, 255, 243, 0.2);
  border: 1px solid #13fff3;
  color: #13fff3;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  gap: 8px;
  margin: 0 auto 15px;
  width: 90%;
  max-width: 200px;
}

.new-chat-btn:hover {
  background-color: rgba(19, 255, 243, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(19, 255, 243, 0.25);
}

.new-chat-btn .el-icon {
  font-size: 18px;
  color: #13fff3;
}

/* 主聊天区域样式 */
main {
  flex: 1;
  width: 100%;
  margin-left: 0;
  padding: 0 25px;
  position: relative;
  height: 100%;
  transition: all 0.3s;
  overflow: hidden;
}

main.sidebar-expanded {
  width: calc(100% - 260px);
  margin-left: 260px;
}

/* 预览激活时的主区域样式 */
main.preview-active {
  /* 移除整个主区域的左移，不应移动顶部按钮 */
  /* transform: translateX(-10%); */
  /* 确保没有任何可能导致内容移动的CSS属性 */
  transition: none;
}

/* 预览激活时的输入区域样式 */
.input-area.preview-active {
  /* 保持原样，确保与消息区域保持一致的偏移 */
  width: 100%;
  transform: translateX(-10%) translateY(0); /* 只水平移动，与消息区域保持一致的左移 */
  transition: all 0.3s ease;
}

.input-area.preview-active .input-container {
  /* 保持与普通状态相同的最大宽度和对齐方式 */
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  /* 不需要向右调整额外内边距，保持与普通状态一致 */
  padding: 8px 15px;
  transition: all 0.3s ease;
}

/* 聊天输入区域样式 */
.input-area {
  position: absolute; /* 恢复为absolute定位 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 0 20px;
  z-index: 5;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  bottom: v-bind("isNewSession ? (chatWindow?.talkInfo?.length ? '20px' : '45%') : '20px'"); /* 仅在新会话时动态变化位置 */
}

/* 欢迎文本位置调整 */
.greeting-text {
  position: absolute;
  bottom: calc(100% + 30px); /* 保持在输入框上方 */
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #fff;
  line-height: 1.8;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 12px;
  opacity: 1; /* 固定为1，通过v-if控制显示隐藏 */
  transition: opacity 0.5s ease;
  pointer-events: none; /* 避免干扰点击 */
  z-index: 4;
}

.greeting-main {
  font-size: 24px;
  font-weight: 500;
  color: #13fff3;
  text-shadow: 0 0 15px rgba(19, 255, 243, 0.4);
  letter-spacing: 1px;
}

.greeting-sub {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.input-container {
  width: 90%;
  max-width: 800px;
  display: flex;
  align-items: center;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 20px;
  padding: 8px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.input-wrapper {
  flex: 1;
  margin: 0 10px;
}

.chat-input {
  width: 100%;
  background-color: transparent;
  border: none;
  color: white;
  font-size: 16px;
  padding: 10px 0;
  outline: none;
  resize: none;
  overflow-y: auto;
  line-height: 1.5;
  max-height: 150px;
  display: block;
  font-family: inherit;
}

.chat-input::-webkit-scrollbar {
  width: 4px;
}

.chat-input::-webkit-scrollbar-track {
  background: transparent;
}

.chat-input::-webkit-scrollbar-thumb {
  background-color: rgba(63, 99, 168, 0.6);
  border-radius: 4px;
}

.chat-input::-webkit-scrollbar-thumb:hover {
  background-color: rgba(93, 130, 201, 0.8);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.record-btn, .send-btn, .abort-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background: linear-gradient(135deg, #13fff3, #19aeff);
  color: #001e57;
  transition: all 0.3s;
  border: none;
}

.abort-btn {
  background: linear-gradient(135deg, #ff4757, #ff3838);
  color: #fff;
}

.record-btn:hover, .send-btn:hover {
  background: linear-gradient(135deg, #00e6da, #0095ff);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(19, 255, 243, 0.3);
}

.abort-btn:hover {
  background: linear-gradient(135deg, #ff3838, #ff2f2f);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(255, 71, 87, 0.4);
}

.record-btn .el-icon, .send-btn .el-icon, .abort-btn .el-icon {
  font-size: 20px;
  color: #001e57;
  transition: transform 0.3s;
}

.abort-btn .el-icon {
  color: #fff;
}

.record-btn:hover .el-icon, .send-btn:hover .el-icon, .abort-btn:hover .el-icon {
  transform: scale(1.1);
}

.send-btn.loading {
  animation: pulse 1.5s infinite;
  opacity: 0.8;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 通用按钮样式 */
.btns {
  display: inline-block;
  width: 140px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background-color: #001e57;
  color: #fff;
  border: 1px solid #3f63a8;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
}

.btns:hover {
  background-color: #163879;
  border-color: #5d82c9;
}

.btns.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btns.disabled:hover {
  background-color: #001e57;
  border-color: #3f63a8;
}

/* 左上角控制按钮 */
.top-control-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  display: flex;
  gap: 10px;
  z-index: 100; /* 提高 z-index 确保在所有元素之上 */
}

.settings-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(9, 39, 98, 0.8), rgba(12, 30, 66, 0.8));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(63, 99, 168, 0.5);
  color: #d0d9f0;
  position: relative; /* 添加相对定位 */
}

.settings-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, rgba(9, 39, 98, 0.9), rgba(12, 30, 66, 0.9));
  border-color: rgba(19, 255, 243, 0.5);
}

.new-chat-btn-top {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(19, 255, 243, 0.2), rgba(19, 174, 255, 0.2));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(19, 255, 243, 0.4);
  position: relative; /* 添加相对定位 */
}

.new-chat-btn-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, rgba(19, 255, 243, 0.3), rgba(19, 174, 255, 0.3));
}

.new-chat-btn-top .el-icon {
  font-size: 18px;
  color: #13fff3;
}

.settings-btn .el-icon {
  font-size: 20px;
  color: #d0d9f0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .top-control-buttons {
    top: 10px;
    left: 10px;
    gap: 8px;
  }
  
  .settings-btn,
  .new-chat-btn-top {
    width: 36px;
    height: 36px;
  }
  
  .settings-btn .el-icon,
  .new-chat-btn-top .el-icon {
    font-size: 18px;
  }
}

@media screen and (max-width: 480px) {
  .top-control-buttons {
    top: 5px;
    left: 5px;
    gap: 5px;
  }
  
  .settings-btn,
  .new-chat-btn-top {
    width: 32px;
    height: 32px;
  }
  
  .settings-btn .el-icon,
  .new-chat-btn-top .el-icon {
    font-size: 16px;
  }
}

/* 顶部操作栏样式 */
.top-actions {
  position: absolute; /* 恢复为absolute定位 */
  top: 15px;
  right: 25px;
  z-index: 10; /* 保持高z-index确保在文件预览之上 */
  display: flex;
  gap: 15px;
}

.close-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 15px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: rgba(21, 69, 156, 0.7);
  border-color: #5d82c9;
}

/* 对话类型选择器样式 - 更新为水平布局 */
.chat-type-selector {
  margin: 0 auto;
  max-width: 600px;
  width: 70%;
  padding: 15px 10px 20px;
  z-index: 10; /* 提高z-index确保在文件预览之上 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  position: relative; /* 恢复为relative定位 */
}

.current-model-info {
  height: 40px;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  color: #fff;
  flex: 1;
  max-width: 45%;
}

.current-model-info.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.current-model-info.clickable:hover {
  background-color: rgba(15, 45, 90, 0.8);
  border-color: #5d82c9;
  box-shadow: 0 2px 8px rgba(19, 255, 243, 0.2);
}

.current-model-info .settings-icon {
  margin-left: auto;
  font-size: 16px;
  color: #8e9dbb;
  transition: all 0.3s ease;
}

.current-model-info:hover .settings-icon {
  color: #13fff3;
  transform: rotate(30deg);
}

.current-model-info .el-icon {
  color: #13fff3;
  font-size: 16px;
  margin-right: 8px;
}

.model-name {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #13fff3;
}

.loading-text {
  color: #8e9dbb;
  display: flex;
  align-items: center;
  gap: 5px;
}

.loading-text .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 聊天窗口上外边距调整，为对话类型选择器预留空间 */
:deep(.chat-window) {
  margin-top: 0;
}

/* 当预览激活时的样式调整 */
main.preview-active .chat-type-selector {
  /* 移除左移变换，保持固定位置 */
  /* transform: translateX(-10%); */
  transition: none;
  z-index: 10; /* 确保在文件预览之上 */
}

@media (max-width: 768px) {
  .chat-type-selector {
    width: 85%;
    flex-direction: column;
    gap: 10px;
  }
  
  .current-model-info {
    max-width: 100%;
  }
}

/* 模型设置对话框样式 */
:deep(.model-settings-dialog) {
  border-radius: 12px;
  background: linear-gradient(to bottom, rgba(9, 39, 98, 0.97), rgba(12, 30, 66, 0.97));
  border: 1px solid rgba(63, 99, 168, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
}

:deep(.model-settings-dialog .el-dialog__header) {
  margin: 0;
  padding: 15px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(63, 99, 168, 0.5);
}

:deep(.model-settings-dialog .el-dialog__title) {
  color: #13fff3;
  font-size: 18px;
  font-weight: 500;
}

:deep(.model-settings-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff;
}

:deep(.model-settings-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: #13fff3;
}

:deep(.model-settings-dialog .el-dialog__body) {
  padding: 20px;
  color: #ffffff;
}

:deep(.model-settings-dialog .el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid rgba(63, 99, 168, 0.3);
}

.model-settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-label {
  font-size: 16px;
  font-weight: 500;
  color: #d0d9f0;
  margin-bottom: 10px;
  border-left: 3px solid #13fff3;
  padding-left: 10px;
}

.model-select-section, .model-params-section {
  margin-bottom: 10px;
}

.model-select {
  width: 100%;
}

.param-item {
  margin-bottom: 20px;
  padding: 10px 15px;
  background: linear-gradient(to right, rgba(9, 39, 98, 0.3), rgba(12, 30, 66, 0.3));
  border-radius: 8px;
  border: 1px solid rgba(63, 99, 168, 0.3);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.param-label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #d0d9f0;
}

.help-icon {
  color: #5d82c9;
  cursor: pointer;
  transition: color 0.2s;
}

.help-icon:hover {
  color: #13fff3;
}

/* 重新样式化el-select在对话框中的表现 */
:deep(.model-settings-dialog .el-select .el-input__wrapper) {
  background: linear-gradient(to bottom, #0c2d70, #001e57);
  border: 1px solid #3f63a8;
  border-radius: 8px;
  box-shadow: none;
}

:deep(.model-settings-dialog .el-select .el-input__wrapper:hover) {
  border-color: #5d82c9;
  box-shadow: 0 0 0 1px #5d82c9;
}

:deep(.model-settings-dialog .el-input-number) {
  --el-input-number-width: 100px;
  --el-input-bg-color: rgba(15, 45, 90, 0.3);
  --el-input-border-color: rgba(63, 99, 168, 0.7);
  --el-input-hover-border-color: #5d82c9;
}

:deep(.model-settings-dialog .el-slider__runway) {
  background-color: rgba(63, 99, 168, 0.3);
  height: 6px;
}

:deep(.model-settings-dialog .el-slider__bar) {
  background: linear-gradient(to right, #13fff3, #19aeff);
  height: 6px;
}

:deep(.model-settings-dialog .el-slider__button) {
  border: 2px solid #13fff3;
  background-color: #fff;
  width: 16px;
  height: 16px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px;
  color: #8e9dbb;
  border-radius: 8px;
  background: rgba(9, 39, 98, 0.3);
  border: 1px dashed rgba(63, 99, 168, 0.5);
  margin-bottom: 15px;
}

.loading-container .el-icon {
  font-size: 18px;
  color: #13fff3;
  animation: rotating 2s linear infinite;
}

/* 历史聊天记录样式已移至ChatHistory.vue组件 */
.save-form {
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item .label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
}

.kb-select {
  width: 100%;
}

.kb-option {
  display: flex;
  flex-direction: column;
}

.kb-name {
  font-weight: bold;
}

.kb-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.kb-select-dropdown {
  max-height: 300px;
}

/* 新增问答对选择列表样式 */
.qa-pairs-selection {
  margin-bottom: 20px;
}

.qa-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.qa-pairs-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(63, 99, 168, 0.5);
  border-radius: 8px;
  padding: 10px;
  background-color: rgba(9, 39, 98, 0.3);
}

.qa-pairs-list::-webkit-scrollbar {
  width: 6px;
}

.qa-pairs-list::-webkit-scrollbar-track {
  background: rgba(9, 39, 98, 0.1);
  border-radius: 4px;
}

.qa-pairs-list::-webkit-scrollbar-thumb {
  background-color: rgba(63, 99, 168, 0.6);
  border-radius: 4px;
}

.qa-pairs-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(19, 255, 243, 0.5);
}

.qa-pair-item {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed rgba(63, 99, 168, 0.4);
}

.qa-pair-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.qa-pair-content {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
  margin-top: 4px;
}

.qa-question {
  font-weight: bold;
  color: #13fff3;
  margin-bottom: 5px;
  word-break: break-word;
}

.qa-answer {
  color: #d0d9f0;
  word-break: break-word;
}

.no-qa-pairs {
  text-align: center;
  color: #909399;
  padding: 20px;
}

:deep(.el-checkbox__label) {
  width: 100%;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #13fff3;
  border-color: #13fff3;
}

:deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
  background-color: #13fff3;
}

:deep(.el-checkbox__inner:hover) {
  border-color: #13fff3;
}

:deep(.el-checkbox__input.is-focus .el-checkbox__inner) {
  border-color: #13fff3;
}

/* 保存模式选择样式 */
:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  color: #d0d9f0;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #13fff3;
  border-color: #13fff3;
}

:deep(.el-radio__input.is-checked+.el-radio__label) {
  color: #13fff3;
}

:deep(.el-radio__inner:hover) {
  border-color: #13fff3;
}

:deep(.el-dialog.save-dialog) {
  border-radius: 12px;
  background: linear-gradient(to bottom, rgba(9, 39, 98, 0.97), rgba(12, 30, 66, 0.97));
  border: 1px solid rgba(63, 99, 168, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
}

:deep(.save-dialog .el-dialog__header) {
  margin: 0;
  padding: 15px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(63, 99, 168, 0.5);
}

:deep(.save-dialog .el-dialog__title) {
  color: #13fff3;
  font-size: 18px;
  font-weight: 500;
}

:deep(.save-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff;
}

:deep(.save-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: #13fff3;
}

:deep(.save-dialog .el-dialog__body) {
  padding: 20px;
  color: #ffffff;
}

:deep(.save-dialog .el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid rgba(63, 99, 168, 0.3);
}
</style>

