<template>
  <div class="container">
    <div class="title">知识问答助手</div>
    <div style="display: flex; width: 100%; justify-content: flex-end">
      <div
        class="btns"
        @click="router.push('/')">
        返回
      </div>
    </div>

    <div class="table-box">
      <div class="table-title">模型查询</div>
      <div style="padding: 10px">
        <el-form :inline="true" @submit.prevent>
          <el-form-item label="基础模型：">
            <el-select
              v-model="queryForm.baseModel"
              placeholder="请选择基础模型"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in baseModelOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="btns" @click="handleSearch">搜索</div>
            <div class="btns" @click="handleReset">重置</div>
          </el-form-item>
          <div style="flex:1"></div>
          <el-form-item style="float: right; margin-left: auto;">
            <div class="btns" @click="handleImport">模型导入</div>
            <div class="btns" @click="handleExport">模型导出</div>
            <input ref="importInput" type="file" style="display:none" webkitdirectory directory multiple @change="onImportChange" />
          </el-form-item>
        </el-form>
      </div>
      <div style="padding: 10px">
        <el-table :data="modelList.slice((modelCurrentPage - 1) * 5, modelCurrentPage * 5)">
          <el-table-column prop="id" label="编号"></el-table-column>
          <el-table-column prop="version" label="版本"></el-table-column>
          <el-table-column prop="base_model" label="基础模型"></el-table-column>
          <el-table-column prop="description" label="描述"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model="modelCurrentPage"
            :page-size="5"
            :total="modelList.length"
            layout="total, prev, pager, next, jumper"
            @current-change="handleModelCurrentChange"
          />
        </div>
      </div>
    </div>

    <div class="table-box">
      <div class="table-title">大模型科普</div>
      <div class="carousel-container">
        <el-carousel :interval="4000" type="card" height="200px">
          <el-carousel-item v-for="model in modelInfoList" :key="model.title">
            <div class="model-card" @click="handleCardClick(model)">
              <div class="card-content">
                <div class="card-image">
                  <img :src="model.image" :alt="model.title">
                </div>
                <div class="card-text">
                  <h3>{{ model.title }}</h3>
                  <p>{{ model.description }}</p>
                </div>
              </div>
              <div class="card-footer">
                <span>点击查看详情</span>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <!-- Word文件预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="currentModel?.title"
      width="80%"
      :before-close="handleClose"
    >
      <div class="word-preview-container">
        <div ref="wordPreviewContainer" class="words"></div>
        <!-- 加载状态显示 -->
        <div v-if="loading" class="loading-overlay">
          <div class="loading-spinner">加载中...</div>
        </div>
      </div>
    </el-dialog>

    <AddVersion></AddVersion>

    <!-- 添加编辑模型对话框 -->
    <el-dialog 
      title="编辑模型" 
      v-model="editDialogVisible" 
      width="30%"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="版本">
          <el-input v-model="editForm.version" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="editForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmEdit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 填写模型信息对话框 -->
    <el-dialog title="填写模型信息" v-model="importDialogVisible" width="30%">
      <el-form :model="importForm" label-width="100px">
        <el-form-item label="版本">
          <el-input v-model="importForm.version" />
        </el-form-item>
        <el-form-item label="基础模型">
          <el-input v-model="importForm.base_model" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="importForm.description" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item label="模型路径">
          <el-input v-model="importForm.model_path" readonly />
        </el-form-item>
        <el-form-item label="数据集">
          <el-input v-model="importForm.dataset" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImportSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import { useRouter } from "vue-router";
  import { ref, computed, watch, onMounted, nextTick, getCurrentInstance } from "vue";
  const { proxy } = getCurrentInstance();
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  import AddVersion from "./components/AddVersion.vue";
  import axios from 'axios';
  import { renderAsync } from 'docx-preview';

  const { addVersionShow } = storeToRefs(useDialogStore());
  const router = useRouter();
  const wordPreviewContainer = ref(null);

  // 添加查询表单数据和方法
  const queryForm = ref({
    baseModel: ''
  });

  // 原始模型列表数据
  const originalModelList = ref([]);
  const modelList = ref([]);

  // 基础模型选项集合
  const baseModelOptions = ref([]);

  // 获取模型列表
  const fetchModelList = async () => {
    try {
      const response = await axios.get('/dev-api/model-structure');

      // 检查响应结构
      if (response.data.code !== 0) {
        throw new Error(response.data.message || '获取模型列表失败');
      }

      // 正确解析数据：response.data.data 才是实际的模型列表
      const modelData = response.data.data || [];
      originalModelList.value = modelData.map(model => ({
        id: model.id,
        version: model.version,
        base_model: model.base_model,
        description: model.description,
        status: false // 默认离线状态，如果需要可以从后端获取
      }));
      modelList.value = [...originalModelList.value];

      // 提取所有不重复的基础模型作为选项
      const baseModels = new Set(originalModelList.value.map(model => model.base_model));
      baseModelOptions.value = Array.from(baseModels);
    } catch (error) {
      console.error('获取模型列表失败:', error);
      proxy.$message.error('获取模型列表失败: ' + (error.message || error));
    }
  };

  // 搜索方法
  const handleSearch = () => {
    modelList.value = originalModelList.value.filter(item => {
      const baseModelMatch = !queryForm.value.baseModel || 
        item.base_model === queryForm.value.baseModel;
      return baseModelMatch;
    });
    modelCurrentPage.value = 1; // 重置页码
  };

  // 重置方法
  const handleReset = () => {
    queryForm.value.baseModel = '';
    modelList.value = [...originalModelList.value];
    modelCurrentPage.value = 1; // 重置页码
  };

  // 在 onMounted 中调用获取模型列表
  onMounted(async () => {
    await fetchModelList();
  });

  // 修改模型信息列表，添加Word文件路径
  const modelInfoList = ref([
    {
      title: 'deepseek系列模型',
      description: '深度求索（DeepSeek），成立于2023年，专注于研究世界领先的通用人工智能底层模型与技术，挑战人工智能前沿性难题。自研模型包括deepseek-v3、deepseek-r1等。',
      image: '/assets/images/models/deepseek.png',
      wordFile: '/assets/docs/deepseek.docx'
    },
    {
      title: 'qwen模型',
      description: 'Qwen是阿里巴巴集团Qwen团队研发的大语言模型和大型多模态模型系列。通过高质量数据进行后期微调以贴近人类偏好，Qwen具备自然语言理解、文本生成、视觉理解、音频理解、工具使用、角色扮演、作为AI Agent进行互动等多种能力。',
      image: '/assets/images/models/qwen.png',
      wordFile: '/assets/docs/qwen.docx'
    },
    {
      title: '模型微调',
      description: '大模型微调，是指在预训练模型基础上，通过特定数据集进行有针对性的训练，以提高模型在特定任务上的表现的一种训练方式。',
      image: '/assets/images/models/weitiao.png',
      wordFile: '/assets/docs/weitiao.docx'
    }
  ]);

  // Word预览相关
  const previewVisible = ref(false);
  const currentModel = ref(null);
  const loading = ref(false);

  const handleCardClick = async (model) => {
    currentModel.value = model;
    previewVisible.value = true;
    loading.value = true;
    
    try {
      // 使用 axios 加载文档
      const response = await axios.get(model.wordFile, {
        responseType: 'blob',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      console.log('文档加载成功，准备渲染');
      await renderAsync(new Blob([response.data]), wordPreviewContainer.value, null, {
        className: 'docx-preview',
        inWrapper: true,
        ignoreWidth: true,
        ignoreHeight: true,
        ignoreFonts: true,
        breakPages: false,
        useBase64URL: true
      });
      
      // 等待文档完全渲染
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('文档处理失败:', error);
      proxy.$message.error(`文档加载失败: ${error.message}`);
    } finally {
      loading.value = false;
    }
  };

  const handleClose = () => {
    previewVisible.value = false;
    currentModel.value = null;
    if (wordPreviewContainer.value) {
      wordPreviewContainer.value.innerHTML = '';
    }
  };

  // 添加编辑相关的响应式变量
  const editDialogVisible = ref(false);
  const editForm = ref({
    id: null,
    version: '',
    description: ''
  });

  // 修改编辑模型方法
  const handleEdit = (row) => {
    editForm.value = {
      id: row.id,
      version: row.version,
      description: row.description
    };
    editDialogVisible.value = true;
  };

  // 修改确认编辑方法
  const confirmEdit = async () => {
    try {
      const response = await axios.put(`/dev-api/version/model/${editForm.value.id}`, {
        version: editForm.value.version,
        description: editForm.value.description
      });

      if (response.data.success) {
        proxy.$message.success('更新成功');
        editDialogVisible.value = false;
        await fetchModelList(); // 重新获取列表
      } else {
        throw new Error(response.data.error || '更新失败');
      }
    } catch (error) {
      console.error('更新失败:', error);
      proxy.$message.error('更新失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 修改删除模型方法
  const handleDelete = (row) => {
    proxy.$modal
      .confirm(`是否确认删除该模型 ${row.version}？`)
      .then(async () => {
        try {
          const response = await axios.delete(`/dev-api/version/model/${row.id}`);
          
          if (response.data.success) {
            proxy.$message.success('删除成功');
            await fetchModelList(); // 重新获取列表
          } else {
            throw new Error(response.data.error || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          proxy.$message.error('删除失败: ' + (error.response?.data?.error || error.message));
        }
      })
      .catch(() => {});
  };

  const modelCurrentPage = ref(1);

  const handleModelCurrentChange = (val) => {
    modelCurrentPage.value = val;
  };

  // 模型导入按钮事件
  const importInput = ref(null);

  const handleImport = () => {
    if (importInput.value) {
      importInput.value.value = '';
      importInput.value.click();
    }
  };

  const importDialogVisible = ref(false);
  const importForm = ref({
    version: '',
    base_model: '',
    description: '',
    model_path: '',
    dataset: ''
  });
  const uploadFolderPath = ref('');

  const onImportChange = async (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // 构造 FormData
      const formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        formData.append(`files[${files[i].webkitRelativePath}]`, files[i]);
      }
      const folderName = files[0].webkitRelativePath.split('/')[0];
      formData.append('folderName', folderName);
      // 上传
      const res = await axios.post('/dev-api/version/upload-model-folder', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      if (res.data.success) {
        uploadFolderPath.value = res.data.save_dir;
        importForm.value = {
          version: '',
          base_model: '',
          description: '',
          model_path: res.data.save_dir,
          dataset: ''
        };
        importDialogVisible.value = true;
      } else {
        proxy.$message.error('上传失败: ' + res.data.error);
      }
    }
  };

  const handleImportSubmit = async () => {
    try {
      const res = await axios.post('/dev-api/version/import-model', importForm.value);
      if (res.data.success) {
        proxy.$message.success('模型信息已保存');
        importDialogVisible.value = false;
        await fetchModelList();
      } else {
        proxy.$message.error('保存失败: ' + res.data.error);
      }
    } catch (e) {
      proxy.$message.error('保存失败');
    }
  };

  const handleExport = async () => {
    // 复制导出目录到剪贴板
    try {
      const response = await axios.get('/dev-api/version/export-path');
      const path = response.data.export_path;

      if (path && navigator.clipboard) {
        await navigator.clipboard.writeText(path);
        proxy.$message({
          message: `导出目录路径已复制到剪贴板: ${path}`,
          type: 'success',
          duration: 5000,
          showClose: true
        });
      } else {
        // 如果不支持剪贴板API，显示路径让用户手动复制
        proxy.$message({
          message: `导出目录: ${path || 'data/exports'}`,
          type: 'info',
          duration: 8000,
          showClose: true
        });
      }
    } catch (e) {
      console.error('获取导出路径失败:', e);
      proxy.$message.error('获取导出路径失败');
    }
  };


</script>
<style scoped>
  .container {
    width: 100%;
    height: 100%;
  }

  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }

  .title {
    width: 100%;
    height: 120px;
    margin: 0 auto;
    text-align: center;
    color: #fff;
    font-size: 50px;

    font-family: YouShe, serif;
    background-image: url("/assets/images/nav-bar-bg.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  .table-box {
    width: 99%;
    margin: 10px;
    border: 1px solid #084386;
  }

  .table-title {
    color: #fff;
    width: 100%;
    background: linear-gradient(to right, #298bfd, #083473);
    padding: 10px;
  }

  .run {
    width: 80px;
    height: 20px;
    background-color: #87bd85;
    text-align: center;
    border-radius: 5px;
  }
  .stop {
    width: 80px;
    height: 30px;
    background-color: #506385;
    text-align: center;
    padding: 5px;
    border-radius: 5px;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    padding: 10px;
  }

  :deep(.el-pagination) {
    --el-pagination-text-color: #fff;
    --el-pagination-hover-color: #409eff;
    --el-pagination-button-color: #fff;
    --el-pagination-button-bg-color: transparent;
    --el-pagination-button-disabled-color: #c0c4cc;
    --el-pagination-button-disabled-bg-color: transparent;
    --el-pagination-hover-color: #409eff;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .carousel-container {
    padding: 10px;
  }

  .model-card {
    height: 100%;
    background: linear-gradient(135deg, #1a365d 0%, #2a4365 100%);
    border-radius: 8px;
    padding: 15px;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    transition: transform 0.3s ease;
  }

  .card-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
  }

  .card-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .card-text {
    flex: 1;
  }

  .model-card:hover {
    transform: translateY(-5px);
  }

  .model-card h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #63b3ed;
  }

  .model-card p {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .card-footer {
    text-align: right;
    color: #63b3ed;
    font-size: 12px;
  }

  .word-preview-container {
    height: 70vh;
    position: relative;
    overflow: hidden;
  }

  .words {
    width: 100%;
    height: 100%;
    overflow: auto;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
  }

  /* 加载状态样式 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }

  .loading-spinner {
    color: #fff;
    padding: 15px 30px;
    border-radius: 4px;
    background-color: rgba(9, 39, 98, 0.8);
    font-size: 18px;
  }

  :deep(.docx-preview) {
    background: #fff;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  :deep(.docx-preview .page) {
    margin: 0 auto;
    box-shadow: none;
    border: none;
  }
</style>
