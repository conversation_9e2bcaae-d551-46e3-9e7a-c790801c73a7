<template>
  <div>
    <el-dialog
      v-model="addKnowledgeShow"
      class="ask-dialog">
      <template #header>
        <div class="header">
          {{ addKnowledgeType === "add" ? "新建知识库" : "修改知识库" }}
        </div>
      </template>

      <el-form
        ref="ruleFormRef"
        :model="newForm"
        :rules="rules"
        label-posistion="right"
        label-width="auto">
        <el-form-item
          label="知识库名称："
          prop="kb_name">
          <el-input v-model="newForm.kb_name"></el-input>
        </el-form-item>
        <el-form-item
          label="描述："
          prop="description">
          <el-input
            v-model="newForm.description"
            resize="none"
            type="textarea"
            rows="5"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <div
          class="btns"
          @click="submit(ruleFormRef)">
          确定
        </div>
        <div
          class="btns"
          @click="addKnowledgeShow = false">
          取消
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
  import { ElMessage } from 'element-plus';
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  const { addKnowledgeShow, addKnowledgeType, editKnowledgeForm } = storeToRefs(
    useDialogStore()
  );
  import { ref, watch, reactive } from "vue";

  const ruleFormRef = ref();

  //表单校验
  const rules = reactive({
    kb_name: [
      { required: true, message: "请输入知识库名称", trigger: "blur" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
    ],
    description: [
      { max: 200, message: "描述不能超过200个字符", trigger: "blur" }
    ],
  });

  //新的数据
  const newForm = ref({});

  // 监听表单数据变化
  watch(editKnowledgeForm, (newVal) => {
    if (newVal) {
      newForm.value = {
        kb_name: newVal.kb_name || '',
        description: newVal.description || ''
      };
    }
  });

  //提交表单
  const submit = async (formRef) => {
    if (!formRef) return;

    await formRef.validate(async (valid, fields) => {
      if (valid) {
        try {
          let submitData = {
            kb_name: newForm.value.kb_name,
            description: newForm.value.description || ''
          };

          let response;
          
          if (addKnowledgeType.value === 'edit') {
            // 判断名称和描述是否变化，只传变化的字段
            const oldName = editKnowledgeForm.value.kb_name;
            const oldDesc = editKnowledgeForm.value.description || '';
            const newName = newForm.value.kb_name;
            const newDesc = newForm.value.description || '';
            submitData = { kb_id: editKnowledgeForm.value.kb_id };
            const nameChanged = newName !== oldName;
            const descChanged = newDesc !== oldDesc;
            if (nameChanged) submitData.new_kb_name = newName;
            if (descChanged) submitData.description = newDesc;
            if (!nameChanged && !descChanged) {
              ElMessage.info('没有任何变更，无需提交');
              return;
            }
            response = await fetch('/dev-api/update_knowledge_base', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(submitData)
            });
          } else {
            // 创建知识库
            response = await fetch('/dev-api/create_knowledge_base', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(submitData)
            });
          }

          const result = await response.json();
          
          if (result.code !== 0) {
            throw new Error(result.msg || '操作失败');
          }

          ElMessage.success(result.msg || (addKnowledgeType.value === 'add' ? '创建成功' : '更新成功'));
          addKnowledgeShow.value = false;
          
          // 触发列表刷新
          addKnowledgeType.value = 'upgrade';
        } catch (error) {
          console.error('操作知识库失败:', error);
          ElMessage.error(error.message || '操作失败，请重试');
        }
      } else {
        ElMessage.error('表单格式错误！');
      }
    });
  };
</script>
<style scoped>
  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }
</style>

<style>
  .ask-dialog {
    width: 600px;
  }

  .ask-dialog .el-dialog__header {
    margin-right: 0;
    background: linear-gradient(to right, #298bfd, #053477);
    color: #fff;
    padding: 10px;
  }

  .ask-dialog .el-dialog__headerbtn {
    top: -5px;
  }
</style>
