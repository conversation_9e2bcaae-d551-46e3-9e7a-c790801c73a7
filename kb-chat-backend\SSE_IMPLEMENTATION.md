# SSE 流式响应实现说明

## 实现概述

已实现SSE（Server-Sent Events）流式响应功能，输出标准SSE格式：

```
data: {"code":1,"msg":"success","data":{"content":"嘿~","session_id":"e1bfd313-926e-4262-9ec6-f2e798ecb3a4"}}

data: {"code":1,"msg":"success","data":{"content":"我是豆包啦！是一个超级热心","session_id":"e1bfd313-926e-4262-9ec6-f2e798ecb3a4"}}

...
data: [DONE]

```

## 核心实现

### 1. VO类结构

- **ChatResponseVo**: 聊天响应数据视图对象
- **StreamResponseVo**: SSE流式响应包装器，包含code、msg、data字段
- **SimpleChatResponseVo**: 简化的聊天响应数据视图对象

### 2. 服务层实现

**ChatStreamServiceImpl.chatWithSSE()** 方法：
- 接收用户输入并保存到数据库
- 调用AI模型进行流式响应
- 将每个响应块包装为指定的JSON格式
- 在流结束时添加`[DONE]`标记
- 异步保存完整的AI回复

### 3. 控制器端点

#### `/chat/sse` - 通用聊天流式接口
```java
@PostMapping(value = "/chat/sse")
public ResponseEntity<Flux<String>> chatSSE(@RequestBody ChatRequestDto chatRequestDto)
```

#### `/kb_chat` - 知识库聊天流式接口
```java
@PostMapping(value = "/kb_chat")
public ResponseEntity<Flux<String>> kbChatStream(@RequestBody KbChatRequestDto kbChatRequestDto)
```

**重要Headers设置**:
- `Cache-Control: no-cache`
- `Connection: keep-alive`
- `Access-Control-Allow-Origin: *`
- `X-Accel-Buffering: no`
- `Content-Type: text/event-stream`

## 技术实现要点

### SSE格式处理
- Spring Boot自动为Flux<String>响应添加SSE格式
- 服务层只需返回纯JSON字符串
- 框架自动添加`data:`前缀和换行符

## 请求格式

### 通用聊天请求
```json
{
  "prompt": "你好，请介绍一下自己",
  "session_id": "chat_123456",
  "new_chat": false,
  "chat_type": "chat"
}
```

### 知识库聊天请求
```json
{
  "prompt": "什么是AI？",
  "session_id": "kb_chat_123456", 
  "new_chat": false,
  "kb_name": "default",
  "search_type": "hybrid"
}
```

## 响应格式

### 流式响应格式
每个流式响应块的格式（标准SSE格式）：
```
data: {"code":1,"msg":"success","data":{"content":"响应内容片段","session_id":"会话ID"}}

data: {"code":1,"msg":"success","data":{"content":"下一个片段","session_id":"会话ID"}}

...
data: [DONE]

```

注意：
- Spring Boot自动为SSE响应添加 `data:` 前缀和 `\n\n` 结尾
- 服务层只需返回纯JSON字符串，框架会自动转换为SSE格式
- 每个数据块是一个完整的JSON对象
- 使用 `text/event-stream` Content-Type
- 流结束时返回 `[DONE]` 标记（框架会自动添加data:前缀）
- 前端解析时使用 `line.startsWith('data:')` 而不是 `'data: '`

### JSON数据结构
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "content": "响应内容片段",
    "session_id": "会话ID"
  }
}
```

### 流结束标记
```
data: [DONE]

```

## 使用示例

### 前端JavaScript调用示例
```javascript
const response = await fetch('/chat/sse', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
  },
  body: JSON.stringify({
    prompt: "你好，请介绍一下自己",
    session_id: "chat_123456"
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

let buffer = '';

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  buffer += decoder.decode(value, { stream: true });
  const lines = buffer.split('\n');
  buffer = lines.pop() || '';

  for (const line of lines) {
    if (line.trim() === '') continue;

    if (line.startsWith('data:')) {
      const data = line.slice(5).trim();
      if (!data || data === '[DONE]') continue;

      try {
        const parsed = JSON.parse(data);
        if (parsed.code === 1) {
          console.log('Received:', parsed.data.content);
        }
      } catch (e) {
        console.error('Parse error:', e);
      }
    }
  }
}
```

### cURL测试示例
```bash
# 测试通用聊天
curl -X POST http://localhost:5000/chat/sse \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"prompt":"你好","session_id":"test123"}'

# 测试知识库聊天
curl -X POST http://localhost:5000/kb_chat \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"prompt":"什么是AI？","session_id":"test123","kb_name":"default"}'
```

## 技术特点

1. **真实流式响应**: 使用Spring WebFlux的Flux实现真正的流式输出
2. **JSON格式标准化**: 统一的响应格式，便于前端解析
3. **会话管理**: 支持会话ID管理和历史记录保存
4. **错误处理**: 完善的错误处理和日志记录
5. **异步保存**: 异步保存完整对话，不阻塞流式响应
6. **类型安全**: 使用强类型DTO确保数据结构正确

## 注意事项

1. 确保前端正确设置`Accept: text/event-stream`头
2. 流式响应中每个JSON对象占一行
3. `[DONE]`标记表示流结束
4. 会话ID为空时会自动生成UUID
5. 所有响应都会保存到数据库中
