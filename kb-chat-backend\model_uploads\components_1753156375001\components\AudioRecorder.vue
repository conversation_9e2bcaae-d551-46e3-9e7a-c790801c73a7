<template>
  <div>
    <div
      class="record-tip"
      @click="endRecord"
      v-if="recordTips">
      <div class="record-tip-inner">
        <div
          v-if="recordData"
          class="record-chart">
          <div
            v-for="(item, index) in recordData"
            :key="index"
            class="rec"
            :style="'height:' + (item - 120) * 2 + 'px'"></div>
        </div>
        <p style="font-size: 80px; font-weight: bold">{{ recordNumber }}</p>
        <p style="font-size: 20px; font-weight: 500">正在录音</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['recordingComplete']);

const recordTips = ref(false);
const recordNumber = ref(3);
const recordData = ref(null);
const mediaRecorder = ref(null);
const audioChunks = ref([]);
const isRecording = ref(false);

const startRecord = async () => {
  try {
    audioChunks.value = [];
    isRecording.value = true;
    recordTips.value = true;
    recordNumber.value = 3;

    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        channelCount: 1,
        sampleRate: 16000,
      },
    });

    const audioContext = new AudioContext();
    const source = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048;
    source.connect(analyser);

    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const updateWaveform = () => {
      if (!isRecording.value) return;
      analyser.getByteTimeDomainData(dataArray);
      recordData.value = Array.from(dataArray).map((value) =>
        Math.floor((value - 128) * 0.5 + 120)
      );
      requestAnimationFrame(updateWaveform);
    };

    mediaRecorder.value = new MediaRecorder(stream);

    mediaRecorder.value.ondataavailable = (event) => {
      audioChunks.value.push(event.data);
    };

    mediaRecorder.value.onstop = () => {
      isRecording.value = false;
      stream.getTracks().forEach((track) => track.stop());
      audioContext.close();
      handleRecordingComplete();
    };

    const numberCount = setInterval(() => {
      recordNumber.value--;
      if (recordNumber.value === 0) {
        recordNumber.value = null;
        mediaRecorder.value.start();
        updateWaveform();
        clearInterval(numberCount);
      }
    }, 1000);
  } catch (error) {
    let errorMessage = "录音失败: ";
    if (error.name === "NotFoundError") {
      errorMessage += "未检测到麦克风设备";
    } else if (error.name === "NotAllowedError") {
      errorMessage += "未获得麦克风权限";
    } else {
      errorMessage += error.message;
    }
    ElMessage.error(errorMessage);
    recordTips.value = false;
    isRecording.value = false;
  }
};

const endRecord = () => {
  recordTips.value = false;
  if (mediaRecorder.value && mediaRecorder.value.state === "recording") {
    mediaRecorder.value.stop();
  }
};

const handleRecordingComplete = async () => {
  try {
    const audioBlob = new Blob(audioChunks.value, { type: "audio/wav" });
    emit('recordingComplete', audioBlob);
  } catch (error) {
    ElMessage.error("处理录音失败：" + error.message);
  } finally {
    isRecording.value = false;
    audioChunks.value = [];
  }
};

defineExpose({
  startRecord
});
</script>

<style scoped>
.record-tip {
  position: absolute;
  width: 230px;
  height: 230px;
  border-radius: 50%;
  color: #fff;
  left: 50%;
  top: 50%;
  z-index: 99;
  transform: translate(-50%, -50%);
  border: 20px solid rgba(48, 98, 192, 0.2);
}

.record-tip-inner {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 99;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(48, 98, 192, 0.6);
}

.record-tip p {
  margin: 0;
}

.record-tip-inner:hover {
  background-color: rgba(48, 98, 192, 0.9);
  cursor: pointer;
}

.rec {
  width: 0.1px;
  background-color: #00b1ff;
  max-height: 100px;
}

.record-chart {
  width: 102.4px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 