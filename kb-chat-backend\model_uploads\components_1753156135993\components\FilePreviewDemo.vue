<template>
  <div class="demo-container">
    <div class="left-panel">
      <h1>FilePreview Demo</h1>
      <el-button type="primary" @click="runDemo">运行演示</el-button>
      
      <div class="results">
        <h2>测试结果</h2>
        <div v-for="(result, index) in testResults" :key="index" class="result-item">
          <p><strong>{{ result.name }}</strong>: {{ result.status }}</p>
          <p v-if="result.details">{{ result.details }}</p>
        </div>
      </div>
    </div>
    
    <div class="right-panel">
      <div class="file-preview">
        <h2>文件预览与高亮</h2>
        <div ref="previewContainer" id="previewContainer" class="preview-content"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage, ElButton } from 'element-plus';
import axios from 'axios';
import { renderAsync } from 'docx-preview';

// 模拟后端数据
const mockSources = ref([
  {
    title: '来源文件：最终版-RAG技术框架调研.docx',
    content: 'RAG技术框架\n\n1.1 Standard RAG\n\nStandard RAG架构是RAG技术的基础版本，它将检索与生成结合起来，通过外部数据源增强语言模型的生成能力。在此架构下，系统会根据输入查询，从外部文档中检索相关信息，并将其与语言模型的生成能力结合，从而生成更符合上下文的回答，并且支持实时信息检索，能够在几秒内生成高质量的响应。',
    file_source: '/home/<USER>/work/kb-chat/kb-chat-fronted/src/assets/最终版-RAG技术框架调研.docx'
  },
  {
    title: '来源文件：最终版-RAG技术框架调研.docx',
    content: 'LightRAG 是一个简单快速的检索增强生成（RAG）系统，适用于多种自然语言处理任务，支持OpenAI和Hugging Face语言模型，并提供多种检索模式（naive、local、global、hybrid）。与传统RAG系统不同，LightRAG 结合了知识图谱，通过图结构表示实体及其关系，增强了上下文感知能力',
    file_source: '/home/<USER>/work/kb-chat/kb-chat-fronted/src/assets/最终版-RAG技术框架调研.docx'
  },
  {
    title: '来源文件：最终版-RAG技术框架调研.docx',
    content: 'Iterative RAG 是一种迭代式多跳问答框架，结合动态检索、关键信息摘要和逻辑规划，逐步优化推理过程，减少噪声干扰并提升答案准确性，同时展现出较强的适应性和跨文档推理能力，为复杂任务提供高效且可扩展的解决方案。',
    file_source: '/home/<USER>/work/kb-chat/kb-chat-fronted/src/assets/最终版-RAG技术框架调研.docx'
  },
  {
    title: '来源文件：最终版-RAG技术框架调研.docx',
    content: 'CRUD-RAG 是一个全面的中文基准数据集，专为评估大语言模型在检索增强生成任务中的性能而设计。它通过模拟复杂、多样的检索和生成场景，提供了丰富的任务类型和高质量数据，涵盖了多领域的知识应用。CRUD-RAG 不仅促进了大语言模型在中文环境下的能力测试，还推动了检索与生成相结合的研究发展，为提升模型的事实性和任务适应性提供了强有力的支持。',
    file_source: '/home/<USER>/work/kb-chat/kb-chat-fronted/src/assets/最终版-RAG技术框架调研.docx'
  }
]);

// 测试结果存储
const testResults = ref([]);

// 引用 DOM 元素
const previewContainer = ref(null);

// FilePreview 的方法
const filePreview = {
  // 真实加载文件的方法
  initWord: async (wordUrl, content) => {
    try {
      console.log('加载文档:', wordUrl);
      
      // 使用 fetch 直接加载本地文件
      const response = await fetch(wordUrl.replace('/home/<USER>/work/kb-chat/kb-chat-fronted', ''));
      
      if (!response.ok) {
        throw new Error(`文件加载失败: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      console.log('文档加载成功，准备渲染');
      
      const container = previewContainer.value;
      await renderAsync(blob, container);
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待渲染完成
      
      if (content?.sources?.length) {
        console.log('开始处理高亮，sources:', content.sources);
        await filePreview.highlightContent(content.sources);
      }
    } catch (error) {
      console.error('文档处理失败:', error);
      // 如果加载失败，使用模拟文档
      console.log('使用模拟文档作为备选');
      await createMockDocument();
      
      if (content?.sources?.length) {
        await filePreview.highlightContent(content.sources);
      }
    }
  },
  // 高亮逻辑
  highlightContent: async (sources) => {
    const container = previewContainer.value;
    if (!container) {
      console.error('预览容器不存在');
      return;
    }
    
    console.log('开始高亮处理');
    
    try {
      if (!sources?.length) return;
      
      // 创建一个简单的文本索引
      const textContent = container.textContent;
      console.log(`文档总文本长度: ${textContent.length}`);
      
      // 处理每个源
      for (const source of sources) {
        if (!source.content || typeof source.content !== 'string' || source.content.length < 10) {
          console.log('source没有有效内容，跳过');
          continue;
        }
        
        console.log('处理source:', source.content.substring(0, 100) + '...');
        const sourceText = source.content.trim();
        
        // 提取关键句子
        const sentences = sourceText
          .split(/[。！？\n]/)
          .map(s => s.trim())
          .filter(s => s.length >= 10);
        
        console.log(`提取了 ${sentences.length} 个句子`);
        
        // 为每个句子创建高亮
        for (const sentence of sentences) {
          if (sentence.length < 10) continue;
          
          // 在文档中查找句子
          const index = textContent.indexOf(sentence);
          if (index === -1) continue;
          
          console.log(`找到句子: "${sentence.substring(0, 30)}..." 在位置 ${index}`);
          
          try {
            // 使用 Range API 创建高亮
            highlightTextRange(container, index, index + sentence.length);
          } catch (error) {
            console.error('高亮句子失败:', error);
          }
        }
      }
    } catch (error) {
      console.error('高亮处理出错:', error);
    }
  },
  formatSourceTitle: (title) => (title ? title.replace('来源文件：', '') : ''),
  hasValidSources: (sources) =>
    sources && Array.isArray(sources) && sources.some(source => source && source.title && filePreview.formatSourceTitle(source.title).trim() !== ''),
  getUniqueSources: (sources) => {
    if (!filePreview.hasValidSources(sources)) return [];
    const sourceMap = new Map();
    sources.forEach(source => {
      if (!source || !source.title || !source.file_source) return;
      if (sourceMap.has(source.file_source)) {
        const existing = sourceMap.get(source.file_source);
        if (source.content && !existing.contents.includes(source.content)) {
          existing.contents.push(source.content);
        }
      } else {
        sourceMap.set(source.file_source, {
          title: source.title,
          contents: source.content ? [source.content] : [],
          content: source.content || '',
          file_source: source.file_source
        });
      }
    });
    return Array.from(sourceMap.values());
  }
};

// 使用 Range API 高亮文本范围
function highlightTextRange(container, startIndex, endIndex) {
  // 创建范围
  const range = document.createRange();
  const startInfo = findNodeAndOffsetAtIndex(container, startIndex);
  const endInfo = findNodeAndOffsetAtIndex(container, endIndex);
  
  if (!startInfo || !endInfo) {
    console.log('无法找到文本位置');
    return;
  }
  
  try {
    // 设置范围边界
    range.setStart(startInfo.node, startInfo.offset);
    range.setEnd(endInfo.node, endInfo.offset);
    
    // 创建高亮元素
    const highlightSpan = document.createElement('span');
    highlightSpan.className = 'highlight';
    highlightSpan.style.backgroundColor = 'yellow';
    
    // 如果范围跨越多个节点，需要特殊处理
    if (startInfo.node !== endInfo.node) {
      // 使用 extractContents 和 surroundContents 的组合
      const fragment = range.extractContents();
      highlightSpan.appendChild(fragment);
      range.insertNode(highlightSpan);
    } else {
      // 单个节点内的范围可以直接使用 surroundContents
      range.surroundContents(highlightSpan);
    }
    
    console.log('成功高亮文本');
  } catch (error) {
    console.error('Range API 高亮失败:', error);
    
    // 回退方法：使用简单的文本替换
    try {
      const text = range.toString();
      if (text && text.length > 0) {
        const tempSpan = document.createElement('span');
        tempSpan.className = 'highlight';
        tempSpan.style.backgroundColor = 'yellow';
        tempSpan.textContent = text;
        
        range.deleteContents();
        range.insertNode(tempSpan);
        console.log('使用回退方法成功高亮');
      }
    } catch (fallbackError) {
      console.error('回退高亮也失败:', fallbackError);
    }
  }
}

// 在容器中找到指定索引位置的节点和偏移量
function findNodeAndOffsetAtIndex(container, targetIndex) {
  // 使用树遍历找到指定索引的文本节点
  let currentIndex = 0;
  
  // 递归函数
  function findNodeRecursive(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      const nodeLength = node.textContent.length;
      
      // 检查目标索引是否在当前节点内
      if (currentIndex <= targetIndex && targetIndex < currentIndex + nodeLength) {
        return {
          node: node,
          offset: targetIndex - currentIndex
        };
      }
      
      currentIndex += nodeLength;
    } else {
      // 遍历子节点
      for (let i = 0; i < node.childNodes.length; i++) {
        const result = findNodeRecursive(node.childNodes[i]);
        if (result) return result;
      }
    }
    
    return null;
  }
  
  return findNodeRecursive(container);
}

// 运行测试演示
const runDemo = async () => {
  testResults.value = []; // 清空结果

  try {
    // 尝试加载本地文件
    const filePath = '/src/assets/最终版-RAG技术框架调研.docx';
    await filePreview.initWord(filePath, { sources: mockSources.value });
    testResults.value.push({ name: '加载文件和高亮所有内容', status: '成功' });
  } catch (e) {
    testResults.value.push({ name: '加载文件和高亮所有内容', status: '失败', details: e.message });
  }

  // 测试 2: formatSourceTitle
  const title = '来源文件：最终版-RAG技术框架调研.docx';
  const formatted = filePreview.formatSourceTitle(title);
  testResults.value.push({
    name: '格式化标题',
    status: formatted === '最终版-RAG技术框架调研.docx' ? '成功' : '失败',
    details: `结果: ${formatted}`
  });

  // 测试 3: hasValidSources
  const valid = filePreview.hasValidSources(mockSources.value);
  testResults.value.push({
    name: '检查有效来源',
    status: valid ? '成功' : '失败'
  });
  const invalid = filePreview.hasValidSources([{ title: '' }]);
  testResults.value.push({
    name: '检查无效来源',
    status: !invalid ? '成功' : '失败'
  });

  // 测试 4: getUniqueSources
  const uniqueSources = filePreview.getUniqueSources(mockSources.value);
  testResults.value.push({
    name: '获取唯一来源',
    status: uniqueSources.length === 1 && uniqueSources[0].contents.length === 4 ? '成功' : '失败',
    details: `唯一来源数量: ${uniqueSources.length}, 内容数量: ${uniqueSources[0].contents.length}`
  });

  ElMessage.success('演示完成');
};

// 保留创建模拟文档的函数作为备选
const createMockDocument = async () => {
  // 与之前相同的实现...
};
</script>

<style scoped>
.demo-container {
  display: flex;
  height: 100vh; /* 占满视口高度 */
}

.left-panel {
  width: 30%; /* 左侧固定宽度 */
  padding: 20px;
  background-color: #f0f0f0; /* 浅灰色背景 */
  overflow-y: auto; /* 允许滚动 */
}

.right-panel {
  flex: 1; /* 右侧自适应宽度 */
  padding: 20px;
  background-color: #ffffff; /* 白色背景 */
}

.file-preview {
  height: 100%; /* 占满右侧高度 */
}

.preview-content {
  border: 1px solid #3f63a8;
  padding: 10px;
  height: 80vh; /* 增加高度 */
  overflow-y: auto;
  background-color: #ffffff;
}

.highlight {
  background-color: yellow !important;
  display: inline !important;
  color: inherit !important;
  font-size: inherit !important;
  font-family: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
  border-radius: 2px;
}
</style>