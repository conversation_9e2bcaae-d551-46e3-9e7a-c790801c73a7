package com.ai.aichat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 文件存储配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.storage")
public class FileStorageConfig {

    /**
     * 知识库文件存储根目录
     */
    private String knowledgeBaseRoot = "data/knowledge_bases";

    /**
     * 临时文件目录
     */
    private String tempDir = "data/temp";

    /**
     * 向量数据库存储目录
     */
    private String vectorDbRoot = "data/vector_db";

    /**
     * 最大文件大小（字节）
     */
    private long maxFileSize = 50 * 1024 * 1024; // 50MB

    /**
     * 支持的文件类型
     */
    private String[] allowedFileTypes = {
        ".txt", ".md", ".pdf", ".doc", ".docx", 
        ".xls", ".xlsx", ".ppt", ".pptx", ".csv"
    };

    /**
     * 获取知识库的完整路径
     */
    public String getKnowledgeBasePath(String kbName) {
        return getAbsolutePath(knowledgeBaseRoot) + File.separator + kbName;
    }

    /**
     * 获取知识库文档目录路径
     */
    public String getKnowledgeBaseDocsPath(String kbName) {
        return getKnowledgeBasePath(kbName) + File.separator + "docs";
    }

    /**
     * 获取知识库向量数据库路径
     */
    public String getKnowledgeBaseVectorPath(String kbName) {
        return getAbsolutePath(vectorDbRoot) + File.separator + kbName;
    }

    /**
     * 获取绝对路径
     */
    private String getAbsolutePath(String path) {
        if (path.startsWith("/") || path.contains(":")) {
            // 已经是绝对路径
            return path;
        }
        // 相对路径，转换为绝对路径
        return System.getProperty("user.dir") + File.separator + path;
    }
}
