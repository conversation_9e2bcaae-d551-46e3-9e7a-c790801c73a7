package com.ai.aichat.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

/**
 * 并发向量化服务
 */
@Slf4j
@Service
public class ConcurrentEmbeddingService {

    @Autowired
    private OpenAiEmbeddingModel embeddingModel;

    // 线程池配置
    private final ExecutorService executorService = Executors.newFixedThreadPool(8); // 8个并发线程
    private final int BATCH_SIZE = 10; // 每批处理10个文本片段

    /**
     * 并发向量化文本列表
     * 
     * @param textList 文本列表
     * @param progressCallback 进度回调函数 (processedCount, totalCount) -> void
     * @return 向量列表
     */
    public List<float[]> embedConcurrently(List<String> textList, BiConsumer<Integer, Integer> progressCallback) {
        if (textList == null || textList.isEmpty()) {
            return new ArrayList<>();
        }

        int totalCount = textList.size();
        log.info("开始并发向量化处理: {} 个文本片段，批大小: {}", totalCount, BATCH_SIZE);

        // 分批处理
        List<List<String>> batches = createBatches(textList, BATCH_SIZE);
        List<CompletableFuture<List<float[]>>> futures = new ArrayList<>();
        
        // 用于跟踪进度
        AtomicInteger processedCount = new AtomicInteger(0);

        // 提交所有批次任务
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            List<String> batch = batches.get(i);
            
            CompletableFuture<List<float[]>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.debug("处理批次 {}/{}: {} 个文本", batchIndex + 1, batches.size(), batch.size());
                    
                    List<float[]> batchVectors = new ArrayList<>();
                    for (String text : batch) {
                        float[] vector = embeddingModel.embed(text);
                        batchVectors.add(vector);
                        
                        // 更新进度
                        int currentProcessed = processedCount.incrementAndGet();
                        if (progressCallback != null) {
                            progressCallback.accept(currentProcessed, totalCount);
                        }
                    }
                    
                    log.debug("批次 {} 处理完成: {} 个向量", batchIndex + 1, batchVectors.size());
                    return batchVectors;
                    
                } catch (Exception e) {
                    log.error("批次 {} 处理失败", batchIndex + 1, e);
                    throw new RuntimeException("向量化批次处理失败", e);
                }
            }, executorService);
            
            futures.add(future);
        }

        // 等待所有任务完成并合并结果
        List<float[]> allVectors = new ArrayList<>();
        try {
            for (CompletableFuture<List<float[]>> future : futures) {
                List<float[]> batchVectors = future.get(30, TimeUnit.MINUTES); // 30分钟超时
                allVectors.addAll(batchVectors);
            }
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.error("并发向量化处理失败", e);
            throw new RuntimeException("并发向量化处理失败", e);
        }

        log.info("并发向量化处理完成: {} 个向量", allVectors.size());
        return allVectors;
    }

    /**
     * 将列表分批
     */
    private List<List<String>> createBatches(List<String> list, int batchSize) {
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 关闭线程池
     */
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取线程池状态信息
     */
    public String getThreadPoolStatus() {
        if (executorService instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) executorService;
            return String.format("ThreadPool[Active: %d, Queue: %d, Completed: %d]",
                    tpe.getActiveCount(), tpe.getQueue().size(), tpe.getCompletedTaskCount());
        }
        return "ThreadPool status unavailable";
    }
}
