package com.ai.aichat.controller;

import com.ai.aichat.model.dto.qa.*;
import com.ai.aichat.model.vo.qa.QaHistoryVo;
import com.ai.aichat.model.vo.qa.QaListVo;
import com.ai.aichat.model.vo.response.ApiResponse;
import com.ai.aichat.service.QaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 问答对管理控制器
 */
@Tag(name = "问答对管理接口")
@RequiredArgsConstructor
@RestController
public class QaController {

    private final QaService qaService;

    @Operation(summary = "获取问答库列表")
    @GetMapping("/list_qa")
    public ApiResponse<QaListVo> listQa() {
        QaListVo result = qaService.listQa();
        return ApiResponse.success(result);
    }

    @Operation(summary = "创建问答库")
    @PostMapping("/create_qa")
    public ApiResponse<String> createQa(@Valid @RequestBody QaBaseCreateDto dto) {
        String result = qaService.createQa(dto);
        return ApiResponse.success(result, "问答库创建成功");
    }

    @Operation(summary = "更新问答库")
    @PutMapping("/update_qa")
    public ApiResponse<String> updateQa(@Valid @RequestBody QaBaseUpdateDto dto) {
        String result = qaService.updateQa(dto);
        return ApiResponse.success(result, "问答库更新成功");
    }

    @Operation(summary = "删除问答库")
    @DeleteMapping("/delete_qa_base")
    public ApiResponse<String> deleteQaBase(@Valid @RequestBody QaBaseDeleteDto dto) {
        String result = qaService.deleteQaBase(dto);
        return ApiResponse.success(result, "问答库删除成功");
    }

    @Operation(summary = "获取问答对历史")
    @GetMapping("/get_qa_history")
    public ApiResponse<QaHistoryVo> getQaHistory(
            @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
        QaHistoryVo result = qaService.getQaHistory(kbName, fileName);
        return ApiResponse.success(result);
    }

    @Operation(summary = "删除问答对文件")
    @DeleteMapping("/delete_qa")
    public ApiResponse<String> deleteQa(
            @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
        String result = qaService.deleteQa(kbName, fileName);
        return ApiResponse.success(result, "删除成功");
    }

    @Operation(summary = "保存问答对")
    @PostMapping("/save_chat")
    public ApiResponse<String> saveChat(@Valid @RequestBody SaveChatDto dto) {
        String result = qaService.saveChat(dto);
        return ApiResponse.success(result, "问答对保存成功");
    }

    @Operation(summary = "检查文件是否存在")
    @GetMapping("/check_file_exists")
    public ApiResponse<Boolean> checkFileExists(
            @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
        boolean exists = qaService.checkFileExists(kbName, fileName);
        return ApiResponse.success(exists);
    }
}
