package com.ai.aichat.service.impl;

import com.ai.aichat.mapper.TrainingTaskMapper;
import com.ai.aichat.model.dto.request.TrainingRequestDto;
import com.ai.aichat.model.entity.TrainingTask;
import com.ai.aichat.model.vo.response.TrainingTaskVo;
import com.ai.aichat.service.ModelStructureService;
import com.ai.aichat.service.TrainingTaskService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 训练任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingTaskServiceImpl extends ServiceImpl<TrainingTaskMapper, TrainingTask> 
        implements TrainingTaskService {

    private final ModelStructureService modelStructureService;

    @Override
    public TrainingTaskVo startTraining(TrainingRequestDto requestDto) {
        try {
            // 检查模型是否存在
            modelStructureService.getModelStructureById(requestDto.getModelId());

            // 创建训练任务
            TrainingTask task = new TrainingTask();
            task.setModelId(requestDto.getModelId());
            task.setDatasetName(requestDto.getDataset());
            task.setEpochs(requestDto.getEpochs());
            task.setLearningRate(requestDto.getLearningRate());
            task.setStatus(0); // 待开始
            task.setCurrentEpoch(0);
            task.setProgress(BigDecimal.ZERO);

            boolean saved = this.save(task);
            if (!saved) {
                throw new RuntimeException("创建训练任务失败");
            }

            // 更新模型状态为训练中
            modelStructureService.updateTrainingStatus(requestDto.getModelId(), 1);

            // 启动训练任务（异步执行）
            startTrainingAsync(task);

            return convertToVo(task);
        } catch (Exception e) {
            log.error("开始训练失败", e);
            throw new RuntimeException("开始训练失败", e);
        }
    }

    @Override
    public List<TrainingTaskVo> getTrainingTasks() {
        try {
            LambdaQueryWrapper<TrainingTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TrainingTask::getCreateTime);
            
            List<TrainingTask> tasks = this.list(queryWrapper);
            
            return tasks.stream().map(this::convertToVo).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取训练任务列表失败", e);
            throw new RuntimeException("获取训练任务列表失败", e);
        }
    }

    @Override
    public TrainingTaskVo getTrainingTaskById(Long id) {
        try {
            TrainingTask task = this.getById(id);
            if (task == null) {
                throw new RuntimeException("训练任务不存在");
            }
            return convertToVo(task);
        } catch (Exception e) {
            log.error("获取训练任务失败，ID: {}", id, e);
            throw new RuntimeException("获取训练任务失败", e);
        }
    }

    @Override
    public TrainingTaskVo getLatestTrainingTask() {
        try {
            TrainingTask task = baseMapper.getLatestTrainingTask();
            if (task == null) {
                return null;
            }
            return convertToVo(task);
        } catch (Exception e) {
            log.error("获取最新训练任务失败", e);
            throw new RuntimeException("获取最新训练任务失败", e);
        }
    }

    @Override
    public Boolean updateTrainingTaskStatus(Long taskId, Integer status, Integer currentEpoch,
                                          BigDecimal loss, BigDecimal accuracy, BigDecimal progress) {
        try {
            LambdaUpdateWrapper<TrainingTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TrainingTask::getId, taskId)
                        .set(TrainingTask::getStatus, status)
                        .set(currentEpoch != null, TrainingTask::getCurrentEpoch, currentEpoch)
                        .set(loss != null, TrainingTask::getLoss, loss)
                        .set(accuracy != null, TrainingTask::getAccuracy, accuracy)
                        .set(progress != null, TrainingTask::getProgress, progress);

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("更新训练任务状态失败，任务ID: {}", taskId, e);
            throw new RuntimeException("更新训练任务状态失败", e);
        }
    }

    @Override
    public Boolean updateTrainingTaskLog(Long taskId, String logContent) {
        try {
            LambdaUpdateWrapper<TrainingTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TrainingTask::getId, taskId)
                        .set(TrainingTask::getTrainingLog, logContent);

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("更新训练任务日志失败，任务ID: {}", taskId, e);
            throw new RuntimeException("更新训练任务日志失败", e);
        }
    }

    @Override
    public Boolean completeTrainingTask(Long taskId, Boolean success, String errorMessage) {
        try {
            TrainingTask task = this.getById(taskId);
            if (task == null) {
                throw new RuntimeException("训练任务不存在");
            }

            LambdaUpdateWrapper<TrainingTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TrainingTask::getId, taskId)
                        .set(TrainingTask::getStatus, success ? 2 : 3) // 2-完成，3-失败
                        .set(TrainingTask::getEndTime, new Date())
                        .set(!success && errorMessage != null, TrainingTask::getErrorMessage, errorMessage)
                        .set(success, TrainingTask::getProgress, BigDecimal.valueOf(100));

            boolean updated = this.update(updateWrapper);

            // 更新模型状态
            if (updated) {
                modelStructureService.updateTrainingStatus(task.getModelId(), success ? 2 : 3);
            }

            return updated;
        } catch (Exception e) {
            log.error("完成训练任务失败，任务ID: {}", taskId, e);
            throw new RuntimeException("完成训练任务失败", e);
        }
    }

    @Override
    public Boolean cancelTrainingTask(Long taskId) {
        try {
            TrainingTask task = this.getById(taskId);
            if (task == null) {
                throw new RuntimeException("训练任务不存在");
            }

            LambdaUpdateWrapper<TrainingTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TrainingTask::getId, taskId)
                        .set(TrainingTask::getStatus, 4) // 4-已取消
                        .set(TrainingTask::getEndTime, new Date());

            boolean updated = this.update(updateWrapper);

            // 更新模型状态为未训练
            if (updated) {
                modelStructureService.updateTrainingStatus(task.getModelId(), 0);
            }

            return updated;
        } catch (Exception e) {
            log.error("取消训练任务失败，任务ID: {}", taskId, e);
            throw new RuntimeException("取消训练任务失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> getTrainingLog() {
        return List.of();
    }

    /**
     * 异步启动训练任务
     */
    private void startTrainingAsync(TrainingTask task) {
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为训练中
                updateTrainingTaskStatus(task.getId(), 1, 0, null, null, BigDecimal.ZERO);

                // 模拟训练过程
                simulateTraining(task);

            } catch (Exception e) {
                log.error("训练任务执行失败，任务ID: {}", task.getId(), e);
                completeTrainingTask(task.getId(), false, e.getMessage());
            }
        });
    }

    /**
     * 模拟训练过程
     */
    private void simulateTraining(TrainingTask task) {
        try {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("开始训练...\n");
            logBuilder.append(String.format("模型ID: %d\n", task.getModelId()));
            logBuilder.append(String.format("数据集: %s\n", task.getDatasetName()));
            logBuilder.append(String.format("训练轮次: %d\n", task.getEpochs()));
            logBuilder.append(String.format("学习率: %s\n", task.getLearningRate()));
            logBuilder.append("=".repeat(50) + "\n");

            // 更新初始日志
            updateTrainingTaskLog(task.getId(), logBuilder.toString());

            // 模拟训练过程
            for (int epoch = 1; epoch <= task.getEpochs(); epoch++) {
                // 模拟每轮训练耗时
                Thread.sleep(1000); // 1秒一轮，实际训练会更久

                // 模拟训练指标
                BigDecimal loss = BigDecimal.valueOf(1.0 - (double) epoch / task.getEpochs() * 0.8 + Math.random() * 0.1);
                BigDecimal accuracy = BigDecimal.valueOf((double) epoch / task.getEpochs() * 0.9 + Math.random() * 0.1);
                BigDecimal progress = BigDecimal.valueOf((double) epoch / task.getEpochs() * 100);

                // 更新训练状态
                updateTrainingTaskStatus(task.getId(), 1, epoch, loss, accuracy, progress);

                // 更新日志
                logBuilder.append(String.format("Epoch %d/%d - Loss: %.4f - Accuracy: %.4f - Progress: %.1f%%\n",
                    epoch, task.getEpochs(), loss.doubleValue(), accuracy.doubleValue(), progress.doubleValue()));
                updateTrainingTaskLog(task.getId(), logBuilder.toString());
            }

            // 训练完成
            logBuilder.append("=".repeat(50) + "\n");
            logBuilder.append("训练完成！\n");
            updateTrainingTaskLog(task.getId(), logBuilder.toString());

            completeTrainingTask(task.getId(), true, null);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("训练被中断", e);
        }
    }

    /**
     * 转换为VO对象
     */
    private TrainingTaskVo convertToVo(TrainingTask task) {
        TrainingTaskVo vo = new TrainingTaskVo();
        BeanUtils.copyProperties(task, vo);

        // 设置状态描述
        vo.setStatusDesc(getStatusDesc(task.getStatus()));

        return vo;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "待开始";
            case 1:
                return "训练中";
            case 2:
                return "训练完成";
            case 3:
                return "训练失败";
            case 4:
                return "已取消";
            default:
                return "未知";
        }
    }
}
