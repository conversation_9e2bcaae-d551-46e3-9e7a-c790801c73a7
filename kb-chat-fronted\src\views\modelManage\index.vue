<template>
  <div class="container">
    <div class="title">知识问答助手</div>
    <div style="display: flex; width: 100%; justify-content: flex-end">
      <div
        class="btns"
        @click="router.push('/')">
        返回
      </div>
    </div>

    <div class="table-box">
      <div
        style="
          display: flex;
          width: 100%;
          justify-content: flex-end;
          padding: 10px;
        ">
        <div
          class="btns"
          @click="router.push('/modelChart')">
          查看模型结果
        </div>

      </div>
      <div class="table-title">模型列表</div>
      <div style="padding: 10px">
        <el-table :data="modelList.slice((currentPage - 1) * 5, currentPage * 5)">
          <el-table-column label="编号" prop="id"></el-table-column>
          <el-table-column label="版本" prop="version"></el-table-column>
          <el-table-column label="基础模型" prop="base_model"></el-table-column>
          <el-table-column label="数据集" prop="dataset"></el-table-column>
          <el-table-column label="描述" prop="description"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                link
                type="warning"
                @click="startTrain(scope.row)">
                训练
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model="currentPage"
            :page-size="5"
            :total="modelList.length"
            layout="total, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <div class="table-box">
      <div class="table-title">问答知识库训练数据集</div>
      <div
        style="
          display: flex;
          width: 100%;
          justify-content: flex-end;
          padding: 10px;
        ">
        <div class="btns" @click="router.push('/knowledgeManage?tab=qa')">
          管理问答库
        </div>
      </div>
      <div style="padding: 10px">
        <el-table :data="trainList.slice((trainCurrentPage - 1) * 3, trainCurrentPage * 3)">
          <el-table-column label="编号" prop="id"></el-table-column>
          <el-table-column label="问答库名称" prop="name"></el-table-column>
          <el-table-column label="描述" prop="description"></el-table-column>
          <el-table-column label="问答文件数" prop="fileCount"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                @click="viewQAData(scope.row)"
                link
                type="primary"
                >查看问答数据</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model="trainCurrentPage"
            :page-size="3"
            :total="trainList.length"
            layout="total, prev, pager, next, jumper"
            @current-change="handleTrainCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 在原有弹窗后添加新的训练参数弹窗 -->
    <el-dialog title="训练参数配置" v-model="trainDialogVisible" width="30%">
      <div class="model-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="模型编号">{{ selectedModel.id }}</el-descriptions-item>
          <el-descriptions-item label="模型版本">{{ selectedModel.version }}</el-descriptions-item>
          <el-descriptions-item label="模型描述">{{ selectedModel.description }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-divider></el-divider>
      <el-form :model="trainForm" label-width="120px">
        <el-form-item label="选择问答库">
          <el-select v-model="trainForm.datasetId" placeholder="请选择问答知识库">
            <el-option
              v-for="item in trainList.filter(dataset => dataset.fileCount > 0)"
              :key="item.id"
              :label="`${item.name} (${item.fileCount}个文件)`"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="训练轮次">
          <el-input-number 
            v-model="trainForm.epochs" 
            :min="10"
            :max="1000"
            :step="10"
            controls-position="right"
            placeholder="请输入训练轮次">
          </el-input-number>
        </el-form-item>
        <el-form-item label="学习率">
          <el-input-number 
            v-model="trainForm.learningRate" 
            :precision="4"
            :step="0.0001"
            :min="0.0001"
            :max="0.01">
          </el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="trainDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmTrain">开始训练</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router';
  import { ref, onMounted, getCurrentInstance } from 'vue';
  import { train } from '../../api/knowledgeManage/train';
  import axios from 'axios';
  const { proxy } = getCurrentInstance();

  const router = useRouter();

  const trainDialogVisible = ref(false); // 控制训练参数弹窗的显示
  const trainForm = ref({
    datasetId: null,
    epochs: 50,
    learningRate: 0.0001
  }); // 存储训练参数

  //模型列表数据
  const modelList = ref([]);

  // 获取模型列表
  const fetchModelList = async () => {
    try {
      const response = await axios.get('/dev-api/model-structure');
      modelList.value = response.data.map(model => ({
        id: model.id,
        version: model.version,
        base_model: model.base_model,
        dataset: model.dataset,
        description: model.description,
        model_path: model.model_path
      }));
    } catch (error) {
      console.error('获取模型列表失败:', error);
      proxy.$message.error('获取模型列表失败');
    }
  };

  // 修改训练集列表为动态获取问答知识库数据
  const trainList = ref([]);

  // 获取问答知识库作为训练数据集
  const fetchDatasetList = async () => {
    try {
      const response = await fetch('/dev-api/list_qa');
      if (response.ok) {
        const result = await response.json();
        if (result.code === 0 && result.data && result.data.kbList) {
          // 转换问答库数据为训练数据集格式
          trainList.value = result.data.kbList.map((qaBase, index) => ({
            id: index + 1,
            name: qaBase.kbName,
            description: `${qaBase.description || '问答知识库'} (${(qaBase.qaFiles || []).length}个文件)`,
            qaFiles: qaBase.qaFiles || [],
            fileCount: (qaBase.qaFiles || []).length,
            kbName: qaBase.kbName // 保存原始问答库名称用于训练
          }));
        } else {
          throw new Error(result.message || result.msg || '获取问答库列表失败');
        }
      } else {
        proxy.$message.error('获取问答知识库列表失败');
      }
    } catch (error) {
      console.error('获取问答知识库列表失败:', error);
      proxy.$message.error('获取问答知识库列表失败');
    }
  };

  // 在script部分添加selectedModel状态
  const selectedModel = ref(null);

  //开始训练
  const startTrain = (row) => {
    selectedModel.value = row; // 保存选中的模型信息
    trainForm.value = {
      datasetId: null,
      epochs: 50,
      learningRate: 0.0001
    };
    trainDialogVisible.value = true;
  };

  // 查看问答数据
  const viewQAData = (dataset) => {
    if (dataset.fileCount === 0) {
      proxy.$message.info('该问答库暂无问答数据');
      return;
    }
    // 跳转到问答库管理页面的问答知识库构建标签页
    router.push('/knowledgeManage?tab=qa');
  };

  // 确认训练
  const confirmTrain = async () => {
    let loading = null;
    let checkInterval = null;

    try {
        const selectedDataset = trainList.value.find(item => item.id === trainForm.value.datasetId);
        if (!selectedDataset) {
            proxy.$message.error('请选择问答知识库');
            return;
        }

        if (selectedDataset.fileCount === 0) {
            proxy.$message.error('选择的问答库没有问答数据，无法进行训练');
            return;
        }

        loading = proxy.$loading({
            lock: true,
            text: '正在启动训练...',
            background: 'rgba(0, 0, 0, 0.7)'
        });

        // 使用问答库名称作为数据集名称
        const response = await axios.post('/dev-api/train', {
            modelId: selectedModel.value.id,
            dataset: selectedDataset.kbName || selectedDataset.name, // 使用问答库名称
            epochs: trainForm.value.epochs,
            learningRate: trainForm.value.learningRate
        });

        if (response.data.success) {
            // 更新模型状态为训练中
            const modelIndex = modelList.value.findIndex(m => m.id === selectedModel.value.id);
            if (modelIndex !== -1) {
                modelList.value[modelIndex].status = true;
            }

            // 关闭训练对话框
            trainDialogVisible.value = false;

            // 定期检查训练日志
            let attempts = 0;
            checkInterval = setInterval(async () => {
                try {
                    const logResponse = await axios.get('/dev-api/training-log');
                    if (logResponse.data && logResponse.data.length > 0) {
                        // 检测到训练日志，说明训练已开始
                        clearInterval(checkInterval);
                        if (loading) {
                            loading.close();
                        }
                        proxy.$message.success('训练任务已开始');
                        router.push('/modelChart');
                    } else if (++attempts >= 30) { // 30秒超时
                        clearInterval(checkInterval);
                        if (loading) {
                            loading.close();
                        }
                        proxy.$message.info('训练正在初始化，请稍后查看训练状态');
                        router.push('/modelChart');
                    }
                } catch (error) {
                    console.error('检查训练日志失败:', error);
                }
            }, 1000); // 每秒检查一次

        } else {
            throw new Error(response.data.error || '启动训练失败');
        }
    } catch (error) {
        console.error('训练请求失败:', error);
        proxy.$message.error('训练启动失败: ' + (error.response?.data?.error || error.message));
        if (loading) {
            loading.close();
        }
        if (checkInterval) {
            clearInterval(checkInterval);
        }
    }
};

  // 修改 onMounted 钩子，同时获取模型列表和数据集列表
  onMounted(async () => {
    await Promise.all([
      fetchModelList(),
      fetchDatasetList()
    ]);
  });

  // 在 script setup 中添加分页相关的变量和方法
  const currentPage = ref(1);

  // 处理页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val;
  };

  // 在 script setup 中添加训练数据集分页相关的变量和方法
  // 训练数据集分页变量
  const trainCurrentPage = ref(1);

  // 处理训练数据集页码变化
  const handleTrainCurrentChange = (val) => {
    trainCurrentPage.value = val;
  };
</script>
<style scoped>
  .container {
    width: 100%;
    height: 100%;
  }

  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url('/assets/images/button/主页按钮-正常.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }

  .title {
    width: 100%;
    height: 120px;
    margin: 0 auto;
    text-align: center;
    color: #fff;
    font-size: 50px;

    font-family: YouShe, serif;
    background-image: url('/assets/images/nav-bar-bg.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  .table-box {
    width: 99%;
    margin: 10px;
    border: 1px solid #084386;
  }

  .table-title {
    color: #fff;
    width: 100%;
    background: linear-gradient(to right, #298bfd, #083473);
    padding: 10px;
  }

  .run {
    width: 80px;
    height: 20px;
    background-color: #1c43a1;
    text-align: center;
    border-radius: 5px;
    color: #fff;
  }
  .stop {
    width: 80px;
    height: 20px;
    background-color: #506385;
    text-align: center;
    border-radius: 5px;
    color: #fff;
  }

  .selected-dataset {
    margin-left: 20px;
    color: #fff;
    font-size: 16px;
  }
  .el-divider {
    margin: 20px 0;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    padding: 10px;
  }

  :deep(.el-pagination) {
    --el-pagination-text-color: #fff;
    --el-pagination-hover-color: #409eff;
    --el-pagination-button-color: #fff;
    --el-pagination-button-bg-color: transparent;
    --el-pagination-button-disabled-color: #c0c4cc;
    --el-pagination-button-disabled-bg-color: transparent;
    --el-pagination-hover-color: #409eff;
  }
</style>
