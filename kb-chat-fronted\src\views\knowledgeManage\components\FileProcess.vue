<template>
  <div>
    <el-dialog
      class="file-process-dialog dark-dialog"
      v-model="fileProcessShow"
      :close-on-click-modal="false"
      width="70%"
      >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">文档处理</span>
        </div>
      </template>

      <!-- 文件上传部分 -->
      <el-upload
        ref="uploadRef"
        action="/dev-api/upload_file"
        :data="uploadData"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-progress="handleProgress"
        :multiple="true"
        :show-file-list="false"
        class="file-upload-area">
        <div class="upload-btn">选择文件</div>
        <template #tip>
          <div class="el-upload__tip">
            支持 txt、pdf、doc、docx、md 等格式文件
          </div>
        </template>
      </el-upload>
      
      <!-- 上传进度条 -->
      <div v-if="uploadingFiles.length > 0" class="upload-progress-container">
        <div class="upload-progress-header">
          <span>正在上传 {{ uploadingFiles.length }} 个文件 ({{ uploadedCount }}/{{ totalUploadCount }})</span>
        </div>
        <el-progress 
          :percentage="totalUploadProgress" 
          :status="totalUploadProgress >= 100 ? 'success' : ''"
          :stroke-width="15"
          :show-text="false"
          class="dark-progress"
        />
        <div class="progress-percent">{{ totalUploadProgress }}%</div>
      </div>

      <!-- 文件状态标签 -->
      <div class="file-status-tabs">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="未处理文件" name="pending">
            <template #label>
              <span>未处理文件 <el-badge :value="statusCounts.pending_count" class="item" /></span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="已处理文件" name="completed">
            <template #label>
              <span>已处理文件 <el-badge :value="statusCounts.completed_count" class="item" /></span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="全部文件" name="all">
            <template #label>
              <span>全部文件 <el-badge :value="statusCounts.total_count" class="item" /></span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 文件列表 -->
      <el-table
        :data="currentTabFiles"
        class="upload-table"
        v-loading="filesLoading"
        element-loading-text="加载中...">
        <el-table-column
          label="名称"
          prop="name"
          min-width="200"
          show-overflow-tooltip>
          <template #default="scope">
            <div class="file-name-cell">
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="大小"
          prop="size">
          <template #default="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="进度"
          prop="progress"
          width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.progress || 0"
              :status="scope.row.progress >= 100 ? 'success' : ''"
              :stroke-width="6"
              :show-text="false"
              v-if="scope.row.status === 'processing'"
            />
            <span v-else>{{ scope.row.progress || 0 }}%</span>
          </template>
        </el-table-column>
        <el-table-column
          label="上传时间"
          prop="upload_time">
          <template #default="scope">
            {{ formatDate(scope.row.upload_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.name, scope.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页控件 -->
      <div class="pagination-container" v-if="total > 0">
        <span class="total-text">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next, jumper"
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          :pager-count="7"
          @current-change="handlePageChange"
          small
        />
        <span class="page-info">{{ currentPage }}/{{ totalPages }} 页</span>
      </div>

      <!-- 处理进度对话框 -->
      <el-dialog
        v-model="processing"
        title="文档处理进度"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
        class="process-dialog dark-dialog">
        <div class="progress-info">
          <!-- 整体进度条 -->
          <div class="overall-progress">
            <div class="progress-label">整体进度</div>
            <el-progress
              :percentage="processingStatus.progress"
              :status="processingStatus.status === '成功' ? 'success' :
                       processingStatus.status === '失败' ? 'exception' : ''"
              :stroke-width="15"
              :show-text="false"
              class="dark-progress"
            />
            <div class="progress-percent">{{ processingStatus.progress }}%</div>
          </div>

          <!-- 处理详情 -->
          <div class="process-details" v-if="processingStatus.processing">
            <div class="detail-item">
              <span class="label">处理文件数：</span>
              <span class="value">{{ processingStatus.processedCount || 0 }} / {{ processingStatus.totalCount || 0 }}</span>
            </div>
            <div class="detail-item" v-if="processingStatus.currentFile">
              <span class="label">当前文件：</span>
              <span class="value file-name">{{ processingStatus.currentFile }}</span>
            </div>
            <div class="detail-item" v-if="processingStatus.chunkCount">
              <span class="label">生成片段：</span>
              <span class="value">{{ processingStatus.chunkCount }}</span>
            </div>
          </div>

          <!-- 状态消息 -->
          <div class="progress-message">
            <p class="status">{{ processingStatus.status }}</p>
            <p class="message">{{ processingStatus.message }}</p>
          </div>

          <!-- 完成后的操作按钮 -->
          <div class="progress-actions" v-if="!processingStatus.processing">
            <el-button
              type="primary"
              @click="closeProcessDialog"
              v-if="processingStatus.status === '成功'">
              完成
            </el-button>
            <el-button
              type="danger"
              @click="closeProcessDialog"
              v-if="processingStatus.status === '失败'">
              关闭
            </el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <div class="process-btn" @click="processDocuments">处理文档</div>
        <div class="cancel-btn" @click="fileProcessShow = false">返回</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDialogStore } from "/src/store/dialog.js";
import { storeToRefs } from "pinia";

const dialogStore = useDialogStore();
const { fileProcessShow, fileProcessForm } = storeToRefs(dialogStore);

// 状态定义
const filesData = ref([]);
const filesLoading = ref(false);
const processing = ref(false);
const processingStatus = ref({
  processing: false,
  progress: 0,
  status: '',
  message: '',
  totalCount: 0,
  processedCount: 0,
  currentFile: '',
  chunkCount: 0
});

// 上传进度相关状态
const uploadingFiles = ref([]);
const totalUploadCount = ref(0);
const uploadedCount = ref(0);
const totalUploadProgress = ref(0);

// 添加分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const totalPages = ref(1);

// 文件状态标签页
const activeTab = ref('pending');

// 状态统计
const statusCounts = ref({
  pending_count: 0,
  completed_count: 0,
  total_count: 0
});

// 上传参数
const uploadData = computed(() => {
  const kbId = fileProcessForm.value?.kb_id;
  console.log('Upload data - kb_id:', kbId, 'fileProcessForm:', fileProcessForm.value);

  return {
    kb_id: kbId || '',
    kb_name: fileProcessForm.value?.kb_name || '' // 添加兼容参数
  };
});

// 文件分类计算属性（简化版，主要用于显示）
const pendingFiles = computed(() => {
  // 这个计算属性现在主要用于兼容性，实际处理时会重新获取
  return filesData.value.filter(file =>
    file.status === 'pending' || file.status === 'processing' || !file.status
  );
});

const completedFiles = computed(() => {
  return filesData.value.filter(file =>
    file.status === 'completed' || file.status === 'success'
  );
});

const currentTabFiles = computed(() => {
  // 后端已经按状态过滤，前端直接显示
  return filesData.value;
});

// 文件上传相关方法
const beforeUpload = (file) => {
  // 检查知识库ID
  if (!fileProcessForm.value?.kb_id && !fileProcessForm.value?.kb_name) {
    ElMessage.error('知识库信息缺失，请重新选择知识库');
    return false;
  }

  console.log('Before upload - kb_id:', fileProcessForm.value?.kb_id, 'kb_name:', fileProcessForm.value?.kb_name);

  // 文件类型验证
  const allowedExtensions = ['.txt', '.pdf', '.doc', '.docx', '.md'];
  const fileName = file.name.toLowerCase();
  const fileExt = '.' + fileName.split('.').pop();

  if (!allowedExtensions.includes(fileExt)) {
    ElMessage.error(`不支持的文件类型: ${fileExt}`);
    return false;
  }

  // 文件大小限制（100MB）
  const maxSize = 100 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过100MB');
    return false;
  }

  // 添加到上传文件列表
  uploadingFiles.value.push({
    uid: file.uid,
    name: file.name,
    size: file.size,
    progress: 0
  });
  
  // 更新总文件数，但不增加已完成计数
  totalUploadCount.value = uploadingFiles.value.length + uploadedCount.value;
  updateTotalProgress();
  
  return true;
};

const handleUploadSuccess = (response, file) => {
  if (response.code !== 0) {
    ElMessage.error(response.msg || '上传失败');
    removeUploadingFile(file);
    return;
  }
  
  // 更新上传进度
  updateFileProgress(file.uid, 100);
  uploadedCount.value++;
  
  // 所有文件上传完成后显示一条成功消息
  if (uploadedCount.value === totalUploadCount.value) {
    ElMessage.success(`全部文件上传成功 (${uploadedCount.value}个)`);
    
    // 清空上传列表
    setTimeout(() => {
      uploadingFiles.value = [];
      totalUploadProgress.value = 0;
      uploadedCount.value = 0;
      totalUploadCount.value = 0;
    }, 1000);
    
    // 上传成功后重新加载文件列表
    loadFileList();
  }
  
  // 从上传中列表移除
  removeUploadingFile(file);
};

const handleUploadError = (error, file) => {
  console.error('文件上传失败:', error);
  let errorMessage = '上传失败';
  
  try {
    if (error.message) {
      const response = JSON.parse(error.message);
      errorMessage = response.message || errorMessage;
    }
  } catch (e) {
    errorMessage = error.message || errorMessage;
  }
  
  ElMessage.error(`${file.name}: ${errorMessage}`);
  removeUploadingFile(file);
};

const handleProgress = (event, file) => {
  const percent = Math.round(event.percent);
  updateFileProgress(file.uid, percent);
};

// 更新单个文件的上传进度
const updateFileProgress = (uid, progress) => {
  const fileIndex = uploadingFiles.value.findIndex(f => f.uid === uid);
  if (fileIndex !== -1) {
    uploadingFiles.value[fileIndex].progress = progress;
    updateTotalProgress();
  }
};

// 移除上传中的文件
const removeUploadingFile = (file) => {
  const index = uploadingFiles.value.findIndex(f => f.uid === file.uid);
  if (index !== -1) {
    uploadingFiles.value.splice(index, 1);
    updateTotalProgress();
  }
};

// 计算总体上传进度
const updateTotalProgress = () => {
  if (totalUploadCount.value === 0) {
    totalUploadProgress.value = 0;
    return;
  }
  
  // 使用已上传完成的文件数占总文件数的百分比
  totalUploadProgress.value = Math.round((uploadedCount.value / totalUploadCount.value) * 100);
};

// 删除文件
const handleDelete = async (name, index) => {
  try {
    const response = await fetch('/dev-api/remove_file_from_kb', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        kbId: fileProcessForm.value.kb_id,
        kbName: fileProcessForm.value.kb_name, // 新增
        fileName: name
      })
    });

    const data = await response.json();
    if (response.ok) {
      ElMessage.success('文件删除成功');
      // 重新加载当前页的文件列表，以获取最新数据
      await loadFileList();
    } else {
      throw new Error(data.error || '删除失败');
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    ElMessage.error(error.message || '删除文件失败');
  }
};

// 获取未处理文件列表的方法
const getPendingFilesList = async () => {
  try {
    let allPendingFiles = [];
    let currentPage = 1;
    let hasMore = true;
    const pageSize = 100; // 使用最大允许的页面大小

    // 分页获取所有未处理文件
    while (hasMore) {
      const response = await fetch(
        `/dev-api/knowledge_base_files?kb_id=${fileProcessForm.value.kb_id}&include_files=true&page=${currentPage}&page_size=${pageSize}&status_filter=pending`
      );
      const result = await response.json();

      if (result.code === 0) {
        const files = result.data.files || [];
        allPendingFiles = allPendingFiles.concat(files);

        // 检查是否还有更多页面
        hasMore = result.data.pagination && result.data.pagination.has_more;
        currentPage++;

        console.log(`获取第${currentPage-1}页未处理文件: ${files.length}个，总计: ${allPendingFiles.length}个`);
      } else {
        console.error('获取未处理文件失败:', result.message);
        break;
      }
    }

    console.log(`总共获取到 ${allPendingFiles.length} 个未处理文件`);
    return allPendingFiles;

  } catch (error) {
    console.error('获取未处理文件失败:', error);
    return [];
  }
};

// 处理文档
const processDocuments = async () => {
  console.log('=== processDocuments called ===');
  console.log('fileProcessForm.value:', fileProcessForm.value);

  // 检查是否有未处理的文件
  if (statusCounts.value.pending_count === 0) {
    console.log('没有未处理的文件');
    ElMessage.warning('没有需要处理的文件');
    return;
  }

  // 获取未处理文件的详细列表
  const pendingFilesList = await getPendingFilesList();
  console.log('pendingFilesList:', pendingFilesList);

  if (pendingFilesList.length === 0) {
    console.log('获取未处理文件列表失败');
    ElMessage.warning('获取未处理文件列表失败');
    return;
  }

  // 确认处理操作
  const confirmResult = await ElMessageBox.confirm(
    `将处理 ${pendingFilesList.length} 个未处理的文件，是否继续？`,
    '确认处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).catch(() => false);

  if (!confirmResult) {
    return;
  }

  processing.value = true;
  processingStatus.value = {
    processing: true,
    progress: 0,
    status: '准备中',
    message: `准备处理 ${pendingFilesList.length} 个文档...`,
    totalCount: pendingFilesList.length,
    processedCount: 0,
    currentFile: '',
    chunkCount: 0
  };

  // 模拟进度更新
  const updateProgress = (progress, currentFile = '', processedCount = 0, chunkCount = 0) => {
    processingStatus.value = {
      ...processingStatus.value,
      progress,
      processedCount,
      currentFile,
      chunkCount,
      message: progress < 100 ?
        `正在处理文档... (${processedCount}/${pendingFilesList.length})` :
        `处理完成！共处理 ${processedCount} 个文件，生成 ${chunkCount} 个片段`
    };
  };

  try {
    // 开始处理
    updateProgress(10, pendingFilesList[0]?.name || '', 0, 0);
    processingStatus.value.status = '处理中';

    const response = await fetch('/dev-api/process_documents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        kb_id: fileProcessForm.value.kb_id,
        kb_name: fileProcessForm.value.kb_name,
        file_names: pendingFilesList.map(f => f.name) // 只处理未处理的文件
      })
    });

    // 模拟处理进度（在等待响应时）
    let progressInterval = setInterval(() => {
      if (processingStatus.value.progress < 80) {
        const newProgress = Math.min(processingStatus.value.progress + 10, 80);
        const estimatedProcessed = Math.floor((newProgress / 100) * pendingFilesList.length);
        updateProgress(
          newProgress,
          pendingFilesList[estimatedProcessed]?.name || '',
          estimatedProcessed,
          estimatedProcessed * 3 // 估算片段数
        );
      }
    }, 1000);

    const data = await response.json();
    clearInterval(progressInterval);

    if (data.code !== 0) {
      throw new Error(data.msg || '处理请求失败');
    }

    // 更新最终处理状态
    const processedFilesCount = Array.isArray(data.data.processed_files) ?
      data.data.processed_files.length :
      (data.data.processed_files || pendingFilesList.length);

    processingStatus.value = {
      processing: false,
      progress: 100,
      status: '成功',
      message: `文档处理完成！`,
      totalCount: pendingFilesList.length,
      processedCount: processedFilesCount,
      currentFile: '',
      chunkCount: data.data.chunk_count || 0
    };

    // 显示处理结果信息
    ElMessage.success(`文档处理完成\n知识库：${data.data.kb_name}\n处理文件数：${processedFilesCount}\n生成片段数：${data.data.chunk_count}`);
    
    // 延迟关闭处理进度对话框
    setTimeout(() => {
      processing.value = false;
      
      // 关闭文件处理对话框前，确保回调函数能正确更新文件数量
      if (props.onProcessComplete) {
        props.onProcessComplete();
      }
      
      // 关闭文件处理对话框
      fileProcessShow.value = false;
    }, 1500);
    
  } catch (error) {
    console.error('处理文档失败:', error);
    clearInterval(progressInterval);

    processingStatus.value = {
      processing: false,
      progress: 0,
      status: '失败',
      message: '处理文档失败：' + error.message,
      totalCount: pendingFilesList.length,
      processedCount: 0,
      currentFile: '',
      chunkCount: 0
    };
    ElMessage.error('处理文档失败：' + error.message);
  }
};

// 关闭处理进度对话框
const closeProcessDialog = async () => {
  processing.value = false;
  // 重新加载文件列表以更新状态
  await loadFileList();
};

// 加载文件列表
const loadFileList = async () => {
  console.log('[loadFileList] called', new Date().toISOString(), 'kb_id:', fileProcessForm.value?.kb_id, 'currentPage:', currentPage.value);
  if (!fileProcessForm.value?.kb_id) return;

  filesLoading.value = true;

  try {
    const response = await fetch(
      `/dev-api/knowledge_base_files?kb_id=${fileProcessForm.value.kb_id}&include_files=true&page=${currentPage.value}&page_size=${pageSize.value}&status_filter=${activeTab.value}`
    );
    const result = await response.json();
    
    if (result.code !== 0) {
      throw new Error(result.msg || "获取文件列表失败");
    }
    
    // 更新文件列表和分页信息
    filesData.value = result.data.files || [];
    
    // 更新分页信息
    if (result.data.pagination) {
      total.value = result.data.pagination.total_count;
      totalPages.value = result.data.pagination.total_pages;
      // 确保当前页不超过总页数
      if (totalPages.value > 0 && currentPage.value > totalPages.value) {
        currentPage.value = totalPages.value;
        if (filesData.value.length === 0 && total.value > 0) {
          // 如果当前页没有数据但总数不为0，重新加载上一页
          await loadFileList();
        }
      }
    } else {
      // 如果返回数据中没有分页信息，则基于文件列表长度设置
      total.value = filesData.value.length;
      totalPages.value = Math.ceil(total.value / pageSize.value);
    }

    // 更新状态统计信息
    if (result.data.status_counts) {
      statusCounts.value = {
        pending_count: result.data.status_counts.pending_count || 0,
        completed_count: result.data.status_counts.completed_count || 0,
        total_count: result.data.status_counts.total_count || 0
      };
    } else {
      // 如果后端没有返回状态统计，则基于当前页数据计算（不准确，但作为兜底）
      const pending = filesData.value.filter(file =>
        file.status === 'pending' || file.status === 'processing' || !file.status
      ).length;
      const completed = filesData.value.filter(file =>
        file.status === 'completed' || file.status === 'success'
      ).length;

      statusCounts.value = {
        pending_count: pending,
        completed_count: completed,
        total_count: filesData.value.length
      };
    }
  } catch (error) {
    console.error("获取文件列表失败:", error);
    ElMessage.error("获取文件列表失败：" + error.message);
    filesData.value = [];
    total.value = 0;
    totalPages.value = 0;
  } finally {
    filesLoading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  loadFileList();
};

// 工具方法
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString('zh-CN');
};

const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 状态相关方法
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'success': '已完成',
    'failed': '失败',
    'error': '错误'
  };
  return statusMap[status] || '未知';
};

const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'success': 'success',
    'failed': 'danger',
    'error': 'danger'
  };
  return typeMap[status] || 'info';
};

// 标签页切换处理
const handleTabChange = async (tabName) => {
  activeTab.value = tabName;
  // 切换标签页时重置到第一页并重新加载数据
  currentPage.value = 1;
  await loadFileList();
};

let lastLoadedKbId = null;

watch([fileProcessShow, fileProcessForm], async ([newShow, newForm], [oldShow, oldForm]) => {
  console.log('[watch] fileProcessShow:', newShow, 'fileProcessForm.kb_id:', newForm?.kb_id, 'old:', oldShow, oldForm?.kb_id, new Date().toISOString());
  if (newShow && !oldShow && newForm?.kb_id) {
    if (lastLoadedKbId !== newForm.kb_id) {
      filesData.value = [];
      currentPage.value = 1;
      lastLoadedKbId = newForm.kb_id;
      console.log('[loadFileList] triggered by dialog open, kb_id:', newForm.kb_id, new Date().toISOString());
      await loadFileList();
    } else {
      console.log('[loadFileList] skipped, same kb_id:', newForm.kb_id, new Date().toISOString());
    }
  } else if (!newShow && oldShow) {
    resetState();
    lastLoadedKbId = null;
  }
});

// 组件挂载时加日志
onMounted(() => {
  console.log('[FileProcess] onMounted', new Date().toISOString());
});

// 重置状态
const resetState = () => {
  // 重置文件列表和分页
  filesData.value = [];
  currentPage.value = 1;
  pageSize.value = 10;
  total.value = 0;
  totalPages.value = 1;
  
  // 重置上传进度
  uploadingFiles.value = [];
  uploadedCount.value = 0;
  totalUploadCount.value = 0;
  totalUploadProgress.value = 0;
  
  // 重置处理状态
  processing.value = false;
  processingStatus.value = {
    processing: false,
    progress: 0,
    status: '',
    message: ''
  };
};

// 定义组件属性
const props = defineProps({
  onProcessComplete: {
    type: Function,
    default: null
  }
});
</script>

<style scoped>
.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: rgba(22, 56, 121, 0.3);
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px dashed #3f63a8;
  transition: all 0.3s;
}

.file-upload-area:hover {
  background-color: rgba(22, 56, 121, 0.5);
  border-color: #0084ff;
}

.upload-btn {
  background: linear-gradient(to right, #0084ff, #0c2d7f);
  color: white;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  display: inline-block;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 132, 255, 0.3);
}

.upload-btn:hover {
  background: linear-gradient(to right, #0084ff, #00a0ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 132, 255, 0.5);
}

.el-upload__tip {
  color: #b1c5da;
  margin-top: 10px;
  font-size: 13px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.process-btn, .cancel-btn {
  padding: 8px 24px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.process-btn {
  background: linear-gradient(to right, #0084ff, #0c2d7f);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 132, 255, 0.3);
}

.process-btn:hover {
  background: linear-gradient(to right, #0084ff, #00a0ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 132, 255, 0.5);
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #b1c5da;
  border: 1px solid #3f63a8;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: #b1c5da;
}

/* 强制设置对话框宽度 */
.file-process-dialog {
  width: auto;
}

:deep(.el-dialog) {
  width: 1100px !important;
  max-width: 95vw !important;
}

.btns:hover {
  filter: contrast(150%) brightness(120%);
}

.progress-info {
  text-align: center;
  padding: 20px;
}

.progress-message {
  margin-top: 15px;
}

.progress-message .status {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
  color: #409eff;
}

.progress-message .status:empty {
  display: none;
}

.progress-message .message {
  color: #b1c5da;
  margin: 5px 0;
}

.upload-table {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.total-text, .page-info {
  color: #b1c5da;
  font-size: 14px;
}

/* 覆盖Element Plus分页组件的样式 */
:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #b1c5da;
  --el-pagination-button-color: #b1c5da;
  --el-pagination-button-bg-color: rgba(13, 20, 59, 0.8);
  --el-pagination-button-disabled-color: #5e6d87;
  --el-pagination-button-disabled-bg-color: rgba(13, 20, 59, 0.5);
  --el-pagination-hover-color: #409eff;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: rgba(13, 20, 59, 0.5);

  border: 1px solid #1e284d;
}

:deep(.el-pagination .el-pagination__jump .el-input__inner) {
  background-color: rgba(29, 41, 94, 0.8);
  color: #b1c5da;
  border-color: #1e284d;
}

:deep(.el-pagination .el-input__wrapper) {
  background-color: rgba(29, 41, 94, 0.8) !important;
  box-shadow: 0 0 0 1px #1e284d inset !important;
}

.upload-progress-container {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(13, 20, 59, 0.8);
  border-radius: 4px;
  border: 1px solid #1e284d;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.upload-progress-header {
  margin-bottom: 10px;
  font-weight: bold;
  color: #b1c5da;
}

.progress-percent {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  font-weight: bold;
  color: #b1c5da;
}

.dark-progress {
  background-color: #1e284d;
}

.el-upload__tip {
  color: #b1c5da;
  line-height: 1.5;
  margin-top: 8px;
}

.el-table {
  --el-table-header-bg-color: rgba(13, 20, 59, 0.8);
  --el-table-header-text-color: #b1c5da;
  --el-table-row-hover-bg-color: rgba(29, 41, 94, 0.5);
  --el-table-tr-bg-color: rgba(13, 20, 59, 0.5);
  --el-table-text-color: #b1c5da;
  --el-table-border-color: #1e284d;
  background-color: transparent;
  margin-top: 20px;
  width: 100%;
}

/* 添加与问答库样式一致的样式 */
.dialog-header {
  width: 100%;
  text-align: center;
  padding: 10px 0;
}

.dialog-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

:deep(.file-process-dialog) {
  background-color: #0f2154;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

:deep(.file-process-dialog .el-dialog__header) {
  background-color: #163879;
  margin: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #0c2d7f;
}

:deep(.file-process-dialog .el-dialog__body) {
  background-color: #0f2154;
  padding: 20px;
  color: #fff;
}

:deep(.file-process-dialog .el-dialog__footer) {
  background-color: #0f2154;
  padding: 10px 20px 20px;
  border-top: 1px solid #0c2d7f;
}

.dark-progress {
  --el-progress-bg-color: #163879;
  --el-progress-color: #0084ff;
}

.progress-percent {
  text-align: center;
  margin-top: 5px;
  color: #0084ff;
  font-weight: bold;
}

.progress-message {
  margin-top: 15px;
  text-align: center;
}

.status {
  font-weight: bold;
  color: #0084ff;
  margin-bottom: 5px;
}

.message {
  color: #b1c5da;
}

/* 文件列表样式 */
:deep(.upload-table) {
  background-color: transparent;
  --el-table-bg-color: #163879;
  --el-table-tr-bg-color: #163879;
  --el-table-header-bg-color: #0c2d7f;
  --el-table-border-color: #0c2d7f;
  --el-table-text-color: #fff;
  --el-table-header-text-color: #b1c5da;
  --el-table-row-hover-bg-color: #1e4b9e;
}

:deep(.upload-table th) {
  background-color: #0c2d7f;
  border-bottom: 1px solid #0a246a;
}

:deep(.upload-table td) {
  border-bottom: 1px solid #0c2d7f;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #1e4b9e;
}

/* 文件名单元格样式 */
.file-name-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.4;
  cursor: pointer;
}

.file-name-cell:hover {
  color: #0084ff;
}

/* 文件状态标签页样式 */
.file-status-tabs {
  margin-bottom: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 15px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #2c3e50;
}

:deep(.el-tabs__item) {
  color: #b1c5da;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #0084ff;
}

:deep(.el-tabs__active-bar) {
  background-color: #0084ff;
}

:deep(.el-badge__content) {
  background-color: #0084ff;
  border: 1px solid #0084ff;
}

/* 分页控件样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #b1c5da;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #b1c5da;
  --el-pagination-hover-color: #0084ff;
  --el-pagination-bg-color: #163879;
  --el-pagination-text-color: #b1c5da;
}

:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump) {
  color: #b1c5da;
}

:deep(.el-pagination .el-pagination__sizes .el-input__wrapper) {
  background-color: #163879;
  border-color: #0084ff;
}

:deep(.el-pagination .el-pagination__sizes .el-input__inner) {
  color: #b1c5da;
}

:deep(.el-pagination .el-pager li) {
  background-color: #163879;
  color: #b1c5da;
}

:deep(.el-pagination .el-pager li.active) {
  background-color: #0084ff;
  color: #fff;
}

:deep(.el-pagination .el-pager li:hover) {
  color: #0084ff;
}

.process-dialog {
  background-color: #0f2154;
  border-radius: 10px;
}

/* 进度对话框内容样式 */
.progress-info {
  padding: 20px 0;
}

.overall-progress {
  margin-bottom: 20px;
}

.progress-label {
  color: #b1c5da;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
}

.progress-percent {
  text-align: center;
  color: #0084ff;
  font-size: 18px;
  font-weight: bold;
  margin-top: 10px;
}

.process-details {
  background-color: rgba(22, 56, 121, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  border: 1px solid rgba(0, 132, 255, 0.2);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #b1c5da;
  font-weight: 500;
}

.detail-item .value {
  color: #fff;
  font-weight: 600;
}

.detail-item .file-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #0084ff;
}

.progress-message {
  margin-top: 20px;
  text-align: center;
}

.progress-message .status {
  color: #0084ff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.progress-message .message {
  color: #b1c5da;
  font-size: 14px;
  line-height: 1.5;
}

.progress-actions {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 132, 255, 0.2);
}

:deep(.process-dialog .el-dialog__header) {
  background-color: #163879;
  margin: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #0c2d7f;
}

:deep(.process-dialog .el-dialog__body) {
  background-color: #0f2154;
  padding: 20px;
  color: #fff;
}
</style>

