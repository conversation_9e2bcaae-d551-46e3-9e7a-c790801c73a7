package com.ai.aichat.controller;

import com.ai.aichat.model.dto.request.TrainingRequestDto;
import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.ai.aichat.model.vo.response.TrainingDatasetVo;
import com.ai.aichat.model.vo.response.TrainingTaskVo;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.ai.aichat.service.ModelStructureService;
import com.ai.aichat.service.TrainingDatasetService;
import com.ai.aichat.service.TrainingTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "模型训练管理", description = "模型微调训练相关接口")
@RequiredArgsConstructor
@RestController
public class ModelTrainingController {

    private final ModelStructureService modelStructureService;
    private final TrainingDatasetService trainingDatasetService;
    private final TrainingTaskService trainingTaskService;

    @Operation(summary = "获取模型结构列表")
    @GetMapping("/model-structure")
    public List<ModelStructureVo> getModelStructures() {
        try {
            log.info("获取模型结构列表");
            return modelStructureService.getAllModelStructures();
        } catch (Exception e) {
            log.error("获取模型结构列表失败", e);
            throw new RuntimeException("获取模型结构列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取训练数据集列表")
    @GetMapping("/datasets")
    public List<TrainingDatasetVo> getTrainingDatasets() {
        try {
            log.info("获取训练数据集列表");
            return trainingDatasetService.getAllTrainingDatasets();
        } catch (Exception e) {
            log.error("获取训练数据集列表失败", e);
            throw new RuntimeException("获取训练数据集列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "开始训练")
    @PostMapping("/train")
    public StreamResponseVo startTraining(@RequestBody TrainingRequestDto requestDto) {
        try {
            log.info("开始训练，请求参数: {}", requestDto);
            
            TrainingTaskVo taskVo = trainingTaskService.startTraining(requestDto);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("taskId", taskVo.getId());
            result.put("message", "训练任务已启动");
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("训练任务启动成功")
                    .data(result)
                    .build();
                    
        } catch (Exception e) {
            log.error("启动训练失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            
            return StreamResponseVo.builder()
                    .code(0)
                    .msg("训练任务启动失败")
                    .data(result)
                    .build();
        }
    }

    @Operation(summary = "获取训练日志")
    @GetMapping("/training-log")
    public List<Map<String, Object>> getTrainingLog() {
        try {
            log.info("获取训练日志");
            
            TrainingTaskVo latestTask = trainingTaskService.getLatestTrainingTask();
            if (latestTask == null) {
                return List.of();
            }
            
            Map<String, Object> logEntry = new HashMap<>();
            logEntry.put("taskId", latestTask.getId());
            logEntry.put("status", latestTask.getStatus());
            logEntry.put("statusDesc", latestTask.getStatusDesc());
            logEntry.put("currentEpoch", latestTask.getCurrentEpoch());
            logEntry.put("epochs", latestTask.getEpochs());
            logEntry.put("progress", latestTask.getProgress());
            logEntry.put("loss", latestTask.getLoss());
            logEntry.put("accuracy", latestTask.getAccuracy());
            logEntry.put("log", latestTask.getTrainingLog());
            logEntry.put("errorMessage", latestTask.getErrorMessage());
            logEntry.put("startTime", latestTask.getStartTime());
            logEntry.put("endTime", latestTask.getEndTime());
            
            return List.of(logEntry);
            
        } catch (Exception e) {
            log.error("获取训练日志失败", e);
            return List.of();
        }
    }

    @Operation(summary = "获取训练任务列表")
    @GetMapping("/training-tasks")
    public StreamResponseVo getTrainingTasks() {
        try {
            log.info("获取训练任务列表");
            
            List<TrainingTaskVo> tasks = trainingTaskService.getTrainingTasks();
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(tasks)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取训练任务列表失败", e);
            return StreamResponseVo.error("获取训练任务列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取训练任务详情")
    @GetMapping("/training-task/{id}")
    public StreamResponseVo getTrainingTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("获取训练任务详情，ID: {}", id);
            
            TrainingTaskVo task = trainingTaskService.getTrainingTaskById(id);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(task)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取训练任务详情失败，ID: {}", id, e);
            return StreamResponseVo.error("获取训练任务详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "取消训练任务")
    @PostMapping("/training-task/{id}/cancel")
    public StreamResponseVo cancelTrainingTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("取消训练任务，ID: {}", id);
            
            Boolean result = trainingTaskService.cancelTrainingTask(id);
            
            Map<String, Object> data = new HashMap<>();
            data.put("cancelled", result);
            data.put("taskId", id);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("训练任务已取消")
                    .data(data)
                    .build();
                    
        } catch (Exception e) {
            log.error("取消训练任务失败，ID: {}", id, e);
            return StreamResponseVo.error("取消训练任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建训练数据集")
    @PostMapping("/datasets")
    public StreamResponseVo createTrainingDataset(@RequestBody Map<String, Object> request) {
        try {
            String name = (String) request.get("name");
            String description = (String) request.get("description");
            String filePath = (String) request.get("filePath");
            
            if (name == null || name.trim().isEmpty()) {
                return StreamResponseVo.error("数据集名称不能为空");
            }
            
            log.info("创建训练数据集，名称: {}", name);
            
            Boolean result = trainingDatasetService.createTrainingDataset(name.trim(), description, filePath);
            
            Map<String, Object> data = new HashMap<>();
            data.put("created", result);
            data.put("name", name.trim());
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("创建成功")
                    .data(data)
                    .build();
                    
        } catch (Exception e) {
            log.error("创建训练数据集失败", e);
            return StreamResponseVo.error("创建训练数据集失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除训练数据集")
    @DeleteMapping("/datasets/{id}")
    public StreamResponseVo deleteTrainingDataset(
            @Parameter(description = "数据集ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("删除训练数据集，ID: {}", id);
            
            Boolean result = trainingDatasetService.deleteTrainingDataset(id);
            
            Map<String, Object> data = new HashMap<>();
            data.put("deleted", result);
            data.put("id", id);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("删除成功")
                    .data(data)
                    .build();
                    
        } catch (Exception e) {
            log.error("删除训练数据集失败，ID: {}", id, e);
            return StreamResponseVo.error("删除训练数据集失败: " + e.getMessage());
        }
    }
}
