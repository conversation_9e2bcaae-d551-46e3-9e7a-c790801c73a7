<template>
  <div class="container">
    <nav class="fixed-nav">
      <!-- Removed search functionality -->
      <div>
        <div class="btns" @click="close">返回</div>
      </div>
    </nav>

    <main class="scrollable-content">
      <div
        class="items"
        v-for="graph in knowledgeGraphs"
        :key="graph.graph_name">
        <div class="graph-header">
          <p class="graph-name">{{ graph.graph_name }}</p>
          <!-- Removed date -->
        </div>

        <!-- New section for description and file count -->
        <div class="graph-info">
          <div class="graph-description-text">存储数据库知识图谱</div>
          <div class="graph-files">
            <img src="/assets/images/文档.png" />
            1个文件 <!-- Assuming 1 file as per user -->
          </div>
        </div>

        <div class="graph-actions">
          <el-button
            @click="viewGraph(graph)"
            link
            type="primary">
            知识图谱可视化
          </el-button>
        </div>
      </div>
    </main>

    <!-- Visualization Dialog -->
    <el-dialog
      v-model="graphDialogVisible"
      :title="`知识图谱 - ${currentGraphName}`"
      width="80%"
      @close="handleGraphDialogClose">
      <div v-loading="loadingGraphData">
        <div
          class="drawBoard"
          ref="graphContainer"></div>
      </div>
    </el-dialog>

    <!-- Node/Edge Detail Dialog -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="detailTitle"
      width="40%">
      <div v-if="selectedItem">
        <p><strong>ID:</strong> {{ selectedItem.id }}</p>
        <p><strong>标签:</strong> {{ selectedItem.label }}</p>
        <div v-if="selectedItem.description">
          <p><strong>描述:</strong> {{ selectedItem.description }}</p>
        </div>
        <div v-if="selectedItem.entity_type">
           <p><strong>类型:</strong> {{ selectedItem.entity_type }}</p>
        </div>
        <div v-if="selectedItem.source">
          <p><strong>来源节点:</strong> {{ selectedItem.source.strip('"') }}</p>
          <p><strong>目标节点:</strong> {{ selectedItem.target.strip('"') }}</p>
        </div>
        <div v-if="selectedItem.weight">
          <p><strong>权重:</strong> {{ selectedItem.weight }}</p>
        </div>
         <div v-if="selectedItem.keywords">
          <p><strong>关键词:</strong> {{ selectedItem.keywords }}</p>
        </div>
        <!-- Add more details as needed -->
      </div>
    </el-dialog>

  </div>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  // 导入 Vis.js 库
  import { Network } from "vis-network/standalone/umd/vis-network.min.js";

  const router = useRouter()

  const knowledgeGraphs = ref([])
  const graphDialogVisible = ref(false)
  const currentGraphName = ref('')
  const currentNodesData = ref([])
  const currentEdgesData = ref([])
  const loadingGraphData = ref(false)
  const graphContainer = ref(null) // Reference to the container div

  // Added for detail dialog
  const detailDialogVisible = ref(false)
  const selectedItem = ref(null)
  const detailTitle = ref('')

  // Removed unused variables and imports from AskBuild.vue

  const fetchKnowledgeGraphs = async () => {
    try {
      // Assuming an API endpoint exists to list knowledge graphs
      const response = await fetch('/dev-api/list_graphs')
      if (!response.ok) {
        throw new Error('获取知识图谱列表失败: ' + response.statusText)
      }
      const data = await response.json()
      console.log('Fetched knowledge graphs:', data); // Add logging
      if (data.code === 0) {
        knowledgeGraphs.value = data.data.graph_list || [] // Assuming data structure
      } else {
        throw new Error(data.msg || '获取知识图谱列表失败')
      }
    } catch (error) {
      console.error('获取知识图谱列表失败:', error)
      ElMessage.error(error.message)
      knowledgeGraphs.value = []
    }
  }

  const viewGraph = async (graph) => {
    currentGraphName.value = graph.graph_name
    graphDialogVisible.value = true
    loadingGraphData.value = true
    currentNodesData.value = []
    currentEdgesData.value = []

    try {
      // Assuming an API endpoint exists to get graph data
      const response = await fetch(`/dev-api/get_graph_data?graph_name=${encodeURIComponent(graph.graph_name)}`)
      const data = await response.json()

      if (data.code === 0) {
        currentNodesData.value = data.data.nodes || [] // Assuming data structure
        currentEdgesData.value = data.data.edges || [] // Assuming data structure
        // Wait for the dialog to be rendered and then create the graph
        nextTick(() => {
          createGraph(currentNodesData.value, currentEdgesData.value)
        })
      } else {
        throw new Error(data.msg || '获取知识图谱数据失败')
      }
    } catch (error) {
      console.error('获取知识图谱数据失败:', error)
      ElMessage.error(error.message)
      currentNodesData.value = []
      currentEdgesData.value = []
    } finally {
      loadingGraphData.value = false
    }
  }

  const createGraph = (nodesData, edgesData) => {
    // Calculate connections
    const nodeConnections = {};
    edgesData.forEach((edge) => {
      nodeConnections[edge.from] = (nodeConnections[edge.from] || 0) + 1;
      nodeConnections[edge.to] = (nodeConnections[edge.to] || 0) + 1;
    });

    // Update node data, set size
    const updatedNodesData = nodesData.map((node) => ({
      ...node,
      size: (nodeConnections[node.id] || 0) * 5 + 10, // Set size based on connection count
      label: node.label, // Ensure label is included
    }));

    const data = {
      nodes: updatedNodesData,
      edges: edgesData,
    };

    const options = {
      height: "700px",
      width: "100%", // Adjust width to take full container
      nodes: {
        shape: "dot",
        font: {
          color: "#ffffff",
        },
      },
      edges: {
        smooth: { type: "continuous" },
        width: 2,
        color: "#848484", // Default edge color
      },
      physics: {
        enabled: true,
      },
      interaction: { // Allow zooming and panning
        navigationButtons: true,
        keyboard: true
      },
      // Removed hardcoded width/height from container style below
    };

    const container = graphContainer.value;
    // Check if container exists before creating network
    if (!container) {
      console.error("Graph container not found");
      return;
    }
    // Clear previous graph if any
    container.innerHTML = '';

    // Map edge data to Vis.js format (source -> from, target -> to)
    const mappedEdgesData = edgesData.map(edge => ({
      id: edge.id,
      from: edge.source, // Map source to from
      to: edge.target,   // Map target to to
      label: edge.label || edge.description || '', // Use label or description as edge label
      title: edge.description || edge.label || '', // Use description or label as hover title
      weight: edge.weight, // Include weight
      keywords: edge.keywords, // Include keywords
      source_id: edge.source_id, // Include source_id
      arrows: 'to', // Assuming directed graph
      // Copy other relevant edge properties if any
      ...edge
    }));

    const network = new Network(container, {
      nodes: updatedNodesData,
      edges: mappedEdgesData, // Use the mapped edge data
    }, options);

    // Add click event listener to nodes
    network.on("click", function (params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            const node = updatedNodesData.find(n => n.id === nodeId);
            if (node) {
                // ElMessage.info(`点击了节点: ${node.label}`); // Removed old message
                selectedItem.value = node;
                detailTitle.value = `节点详情: ${node.label}`;
                detailDialogVisible.value = true;
            }
        } else if (params.edges.length > 0) { // Add edge click listener
            const edgeId = params.edges[0];
            const edge = edgesData.find(e => e.id === edgeId);
             if (edge) {
                selectedItem.value = edge;
                detailTitle.value = `关系详情: ${edge.label || edge.id}`;
                detailDialogVisible.value = true;
            }
        }
    });
  }

  const handleGraphDialogClose = () => {
    graphDialogVisible.value = false
    // Optional: Clear graph data or destroy network instance if needed
  }

  const close = () => {
    router.go(-1)
  }

  onMounted(() => {
    fetchKnowledgeGraphs()
  })
</script>

<style scoped>
/* Adopted styles from AskBuild.vue */
.btns {
  display: inline-block;
  width: 140px;
  height: 30px;
  text-align: center;
  background-image: url("/assets/images/button/主页按钮-正常.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: #b1c5da;
  font-size: 14px;
  line-height: 30px;
  margin-right: 10px;
}

.btns:hover {
  cursor: pointer;
  filter: contrast(150%) brightness(120%);
}

.items {
  background-image: url("/assets/images/知识库底图.png");
  width: 502px;
  height: 309px; /* Adjusted height to fit content */
  position: relative;
  margin: 10px;
}

.graph-header {
  position: absolute;
  left: 100px;
  top: 20px;
  color: #fff;
}

.graph-name {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

/* New styles for info section */
.graph-info {
  position: absolute;
  left: 40px;
  top: 100px; /* Adjust based on header height */
  height: 100px;
  width: 440px;
  color: #0084ff;
  /* overflow-wrap: break-word; */
  /* word-wrap: break-word; */
}

.graph-description-text {
  margin-bottom: 10px; /* Space between description and file count */
}

.graph-files {
  display: flex;
  align-items: center;
  color: #0084ff;
  font-size: 16px;
}

.graph-files img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

/* Removed styles for kb-date, kb-description, kb-files */

.graph-actions {
  position: absolute;
  bottom: 25px;
  right: 40px;
  color: #0084ff;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fixed-nav {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.scrollable-content {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

/* Styles for the visualization dialog content */
.drawBoard {
    width: 100%;
    height: 700px; /* Fixed height for the graph area */
    background-color: #011b53;
    border: none;
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

/* Removed styles for file list and qa history */
</style>

<style>
/* Add some basic styling for the detail dialog content */
.el-dialog__body strong {
  color: #13fff3;
}
.el-dialog__body p {
  margin-bottom: 8px;
  color: #ccc;
}
</style>
