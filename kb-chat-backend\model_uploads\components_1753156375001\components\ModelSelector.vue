<template>
  <div class="model-selector">
    <h3 class="section-title">模型设置</h3>
    
    <!-- 模型选择下拉框 -->
    <div class="select-wrapper">
      <el-select
        v-model="selectedModel"
        placeholder="请选择模型"
        @change="handleModelChange"
        class="model-select"
        popper-class="model-select-dropdown">
        <template #prefix>
          <el-icon><Cpu /></el-icon>
        </template>
        <el-option
          v-for="model in modelList"
          :key="model.path"
          :label="model.name"
          :value="model.path">
          <div class="model-option">
            <span class="model-name" :title="model.name">{{ model.name }}</span>
          </div>
        </el-option>
      </el-select>
      
      <!-- 当前选择的模型展示 -->
      <div v-if="selectedModel" class="selected-model-info">
        <span class="selected-model-label">当前模型:</span>
        <el-tooltip 
          :content="getCurrentModelName()"
          placement="top"
          :show-after="500"
          v-if="getCurrentModelName().length > 25">
          <span class="selected-model-value">{{ truncateModelName(getCurrentModelName(), 25) }}</span>
        </el-tooltip>
        <span v-else class="selected-model-value">{{ getCurrentModelName() }}</span>
      </div>
    </div>

    <!-- 模型参数设置 -->
    <el-collapse v-model="activeNames" class="params-collapse">
      <el-collapse-item name="1">
        <template #title>
          <div class="settings-title">
            <el-icon><Setting /></el-icon>
            <span>生成设置</span>
          </div>
        </template>
        
        <div class="param-item">
          <div class="param-header">
            <span class="param-label">
              最大生成长度
              <el-tooltip
                effect="dark"
                placement="right">
                <template #content>
                  <div class="tooltip-content">
                    控制模型每次回答最多可以生成多少个字符。
                    <br/>较大的值支持更长的回答，但会消耗更多资源和时间。
                  </div>
                </template>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
            <el-input-number 
              v-model="modelConfig.max_new_tokens"
              :min="64"
              :max="8192"
              :step="64"
              size="small"
              class="param-input"
              @change="handleConfigChange"
            />
          </div>
          <el-slider
            v-model="modelConfig.max_new_tokens"
            :min="64"
            :max="8192"
            :step="64"
            :marks="{
              64: '64',
              1024: '1K',
              2048: '2K',
              4096: '4K',
              8192: '8K'
            }"
            class="param-slider"
            @change="handleConfigChange">
          </el-slider>
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <span class="param-label">
              温度
              <el-tooltip
                effect="dark"
                placement="right">
                <template #content>
                  <div class="tooltip-content">
                    控制文本生成的随机性。
                    <br/>值越高(0-2)，生成的内容越具有创造性但可能不太可控；
                    <br/>值越低，生成的内容更加确定和保守。
                  </div>
                </template>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
            <el-input-number 
              v-model="modelConfig.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              :precision="1"
              size="small"
              class="param-input"
              @change="handleConfigChange"
            />
          </div>
          <el-slider
            v-model="modelConfig.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            @change="handleConfigChange">
          </el-slider>
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <span class="param-label">
              Top P
              <el-tooltip
                effect="dark"
                placement="right">
                <template #content>
                  <div class="tooltip-content">
                    控制文本生成的采样范围。
                    <br/>值越高(0-1)，生成的内容更多样；
                    <br/>值越低，生成的内容更加聚焦和确定。
                  </div>
                </template>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
            <el-input-number 
              v-model="modelConfig.top_p"
              :min="0"
              :max="1"
              :step="0.1"
              :precision="1"
              size="small"
              @change="handleConfigChange"
            />
          </div>
          <el-slider
            v-model="modelConfig.top_p"
            :min="0"
            :max="1"
            :step="0.1"
            @change="handleConfigChange">
          </el-slider>
        </div>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 应用按钮 -->
    <el-button 
      type="primary" 
      @click="applyChanges" 
      :loading="loading"
      style="width: 100%; margin-top: 15px;">
      {{ loading ? '应用中...' : '应用设置' }}
    </el-button>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Setting, QuestionFilled, Cpu } from '@element-plus/icons-vue'
import { useDialogStore } from "/src/store/dialog.js";
import { storeToRefs } from "pinia";
// 不再导入 API 方法
// import { getModelConfig, updateModelConfig, reloadModel, getModelList } from '@/api/knowledgeManage/model';

// 添加错误处理，确保store正确初始化
let dialogStore;
let currentModelName;

try {
  dialogStore = useDialogStore();
  if (!dialogStore) {
    console.error('Failed to initialize dialogStore');
  } else {
    const storeRefs = storeToRefs(dialogStore);
    currentModelName = storeRefs.currentModelName;
    
    if (!currentModelName) {
      console.error('currentModelName is not available in dialogStore');
    } else {
      console.log('Store initialized successfully, currentModelName:', currentModelName.value);
    }
  }
} catch (error) {
  console.error('Error initializing dialogStore:', error);
}

const modelList = ref([]);
const selectedModel = ref('');
const activeNames = ref(['1']);
const loading = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API || '/dev-api'; // 获取基础 URL

// 默认模型配置
const modelConfig = reactive({
  max_new_tokens: 2048,
  temperature: 0.7,
  top_p: 0.9
});

// 获取当前选择的模型名称
const getCurrentModelName = () => {
  const model = modelList.value.find(m => m.path === selectedModel.value);
  const modelName = model ? model.name : '';
  // 确保currentModelName存在后再设置值
  if (modelName && currentModelName && typeof currentModelName === 'object') {
    try {
      currentModelName.value = modelName;
      console.log('Updated currentModelName to:', modelName);
    } catch (error) {
      console.error('Error updating currentModelName:', error);
    }
  }
  return modelName;
};

// 监听selectedModel变化，自动更新currentModelName
watch(selectedModel, (newValue) => {
  if (newValue && currentModelName && typeof currentModelName === 'object') {
    const model = modelList.value.find(m => m.path === newValue);
    if (model) {
      try {
        currentModelName.value = model.name;
        console.log('Watch updated currentModelName to:', model.name);
      } catch (error) {
        console.error('Error in watch updating currentModelName:', error);
      }
    }
  }
});

// 截断长模型名称并添加省略号
const truncateModelName = (name, maxLength) => {
  if (!name) return '';
  if (name.length <= maxLength) return name;
  return name.substring(0, maxLength) + '...';
};

// 直接使用 fetch API 获取模型列表
const loadModelList = async () => {
  try {
    loading.value = true;
    
    const response = await fetch(`${baseUrl}/model/list`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const res = await response.json();
    
    if (res.code === 1) {
      modelList.value = res.data;
      
      // 设置当前选中的模型
      const currentModel = modelList.value.find(model => model.is_current);
      if (currentModel) {
        selectedModel.value = currentModel.path;
        // 确保currentModelName存在后再设置值
        if (currentModelName && typeof currentModelName === 'object') {
          currentModelName.value = currentModel.name;
          console.log('当前模型已设置为:', currentModel.name);
        }
      } else if (modelList.value.length > 0) {
        // 如果没有当前模型，选择第一个
        selectedModel.value = modelList.value[0].path;
        // 确保currentModelName存在后再设置值
        if (currentModelName && typeof currentModelName === 'object') {
          currentModelName.value = modelList.value[0].name;
          console.log('未找到当前模型，使用第一个模型:', modelList.value[0].name);
        }
      } else {
        // 如果模型列表为空，显示提示信息
        console.warn('模型列表为空');
        if (currentModelName && typeof currentModelName === 'object') {
          currentModelName.value = "未发现可用模型";
        }
      }
      // 新增：提示模型加载成功
      ElMessage.success('模型加载成功');
    } else {
      throw new Error(res.msg || '获取模型列表失败');
    }
  } catch (error) {
    console.error('获取模型列表错误:', error);
    ElMessage.error(error.message || '获取模型列表失败');
    modelList.value = [];
    
    // 在发生错误时设置一个错误提示
    if (currentModelName && typeof currentModelName === 'object') {
      currentModelName.value = "模型加载失败";
    }
  } finally {
    loading.value = false;
  }
};

// 直接使用 fetch API 获取模型配置
const loadModelConfig = async () => {
  try {
    loading.value = true;
    
    const response = await fetch(`${baseUrl}/model/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const res = await response.json();
    
    if (res.code === 1) {
      // 确保所有配置项都有值
      const config = res.data;
      Object.keys(modelConfig).forEach(key => {
        if (config[key] !== undefined && config[key] !== null) {
          modelConfig[key] = config[key];
        }
      });
    } else {
      throw new Error(res.msg || '获取模型配置失败');
    }
  } catch (error) {
    console.error('获取模型配置错误:', error);
    ElMessage.error(error.message || '获取模型配置失败');
  } finally {
    loading.value = false;
  }
};

// 处理模型切换
const handleModelChange = async (modelPath) => {
  try {
    // 更新当前选择的模型
    const model = modelList.value.find(m => m.path === modelPath);
    if (model && currentModelName && typeof currentModelName === 'object') {
      currentModelName.value = model.name;
    }
    
    // 其余逻辑保持不变
    selectedModel.value = modelPath;
  } catch (error) {
    console.error('处理模型切换错误:', error);
    ElMessage.error(error.message || '处理模型切换失败');
  }
};

// 处理配置变更
const handleConfigChange = () => {
  // 配置变更时的处理逻辑
  // 可以在这里添加验证逻辑
};

// 直接使用 fetch API 应用配置变更
const applyChanges = async () => {
  if (!selectedModel.value) {
    ElMessage.warning('请先选择模型');
    return;
  }
  
  try {
    loading.value = true;
    
    // 更新模型配置
    const configResponse = await fetch(`${baseUrl}/model/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(modelConfig)
    });
    
    const configRes = await configResponse.json();
    
    if (configRes.code !== 1) {
      throw new Error(configRes.msg || '更新模型配置失败');
    }
    
    // 重新加载模型
    const reloadResponse = await fetch(`${baseUrl}/model/reload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model_path: selectedModel.value
      })
    });
    
    const reloadRes = await reloadResponse.json();
    
    if (reloadRes.code !== 1) {
      throw new Error(reloadRes.msg || '重新加载模型失败');
    }
  } catch (error) {
    console.error('应用模型设置错误:', error);
    ElMessage.error(error.message || '应用模型设置失败');
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  try {
    await loadModelList();
    await loadModelConfig();
  } catch (error) {
    console.error('初始化模型选择器错误:', error);
    ElMessage.error('初始化失败，请刷新页面重试');
  }
});
</script>

<style scoped>
.model-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-title {
  margin: 0 0 5px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-left: 3px solid #13fff3;
  padding-left: 10px;
}

.select-wrapper {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-select {
  width: 100%;
}

.model-option {
  display: flex;
  align-items: center;
  width: 100%;
}

.model-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.selected-model-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: rgba(19, 255, 243, 0.08);
  border-radius: 6px;
  font-size: 13px;
}

.selected-model-label {
  color: #8e9dbb;
  white-space: nowrap;
}

.selected-model-value {
  color: #13fff3;
  font-weight: 500;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-select) {
  --el-select-border-color-hover: #5d82c9;
}

:deep(.el-select .el-input__wrapper) {
  background: linear-gradient(to bottom, #0c2d70, #001e57);
  border: 1px solid #3f63a8;
  border-radius: 8px;
  box-shadow: none;
  padding: 1px 15px;
  height: 42px;
  transition: all 0.3s ease;
}

:deep(.el-select .el-input__wrapper:hover) {
  border-color: #5d82c9;
  box-shadow: 0 0 0 1px #5d82c9;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #13fff3;
  box-shadow: 0 0 0 1px #13fff3;
}

:deep(.el-select .el-input__inner) {
  color: #fff;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-select .el-input__prefix) {
  color: #5d82c9;
  margin-right: 8px;
}

/* 折叠面板样式 */
:deep(.params-collapse) {
  border: none;
  background: transparent;
}

:deep(.el-collapse-item__header) {
  background: linear-gradient(to right, rgba(9, 39, 98, 0.5), rgba(12, 30, 66, 0.5));
  border: 1px solid rgba(63, 99, 168, 0.5);
  border-radius: 8px;
  color: #fff;
  height: 42px;
  padding: 0 15px;
  transition: all 0.3s;
}

:deep(.el-collapse-item__header:hover) {
  background: linear-gradient(to right, rgba(15, 55, 130, 0.6), rgba(18, 45, 100, 0.6));
  border-color: #5d82c9;
}

:deep(.el-collapse-item__arrow) {
  color: #5d82c9;
  transition: transform 0.3s;
}

:deep(.el-collapse-item__wrap) {
  background: transparent;
  border: none;
}

:deep(.el-collapse-item__content) {
  padding: 15px 5px;
  color: #fff;
}

/* 参数设置项样式 */
.settings-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.param-item {
  margin-bottom: 20px;
  padding: 10px 15px;
  background: linear-gradient(to right, rgba(9, 39, 98, 0.3), rgba(12, 30, 66, 0.3));
  border-radius: 8px;
  border: 1px solid rgba(63, 99, 168, 0.3);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.param-label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #d0d9f0;
}

.help-icon {
  color: #5d82c9;
  cursor: pointer;
  transition: color 0.2s;
}

.help-icon:hover {
  color: #13fff3;
}

:deep(.tooltip-content) {
  font-size: 12px;
  line-height: 1.5;
}

/* 输入框和滑块样式 */
:deep(.param-input .el-input-number__decrease),
:deep(.param-input .el-input-number__increase) {
  background-color: #0c2d70;
  color: #fff;
  border-color: #3f63a8;
}

:deep(.param-input .el-input__wrapper) {
  background-color: #001e57;
  border-color: #3f63a8;
  box-shadow: none;
}

:deep(.param-input .el-input__inner) {
  color: #fff;
}

:deep(.param-slider .el-slider__runway) {
  background-color: rgba(63, 99, 168, 0.3);
  height: 6px;
  margin: 18px 0;
}

:deep(.param-slider .el-slider__bar) {
  background: linear-gradient(to right, #13fff3, #19aeff);
  height: 6px;
}

:deep(.param-slider .el-slider__button) {
  border: 2px solid #13fff3;
  background-color: #fff;
  width: 16px;
  height: 16px;
  transition: transform 0.2s;
}

:deep(.param-slider .el-slider__button:hover) {
  transform: scale(1.2);
}

:deep(.param-slider .el-slider__marks-text) {
  color: #8e9dbb;
  font-size: 12px;
}

/* 修复可能的布局问题 */
:deep(.el-select-dropdown__item) {
  padding: 0 15px;
}

:deep(.el-collapse-item__content:last-child) {
  padding-bottom: 15px;
}
</style>

<style>
/* 自定义下拉菜单样式 */
.model-select-dropdown {
  background: linear-gradient(to bottom, #0f2a63, #091d45) !important;
  border: 1px solid #3f63a8 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
  margin-top: 5px !important;
  max-width: 300px !important;
}

.model-select-dropdown .el-scrollbar__view {
  padding: 5px 0 !important;
}

.model-select-dropdown .el-select-dropdown__item {
  color: #d0d9f0 !important;
  height: auto !important;
  min-height: 36px !important;
  padding: 8px 15px !important;
  line-height: 1.5 !important;
  white-space: normal !important; /* 允许文本换行 */
  word-break: break-word !important; /* 在需要时断词 */
}

.model-select-dropdown .el-select-dropdown__item.hover,
.model-select-dropdown .el-select-dropdown__item:hover {
  background-color: rgba(19, 255, 243, 0.1) !important;
  color: #13fff3 !important;
}

.model-select-dropdown .el-select-dropdown__item.selected {
  background-color: rgba(19, 255, 243, 0.2) !important;
  color: #13fff3 !important;
  font-weight: bold !important;
}

.model-select-dropdown .el-select-dropdown__item.selected::after {
  content: '✓';
  margin-left: 5px;
  font-weight: bold;
}

.model-select-dropdown .el-popper__arrow::before {
  background: #0f2a63 !important;
  border-color: #3f63a8 !important;
}
</style> 