package com.ai.aichat.service;

import reactor.core.publisher.Flux;

/**
 * 聊天流服务接口，处理流式响应并保存消息到数据库
 */
public interface ChatStreamService {

    /**
     * 处理聊天请求并保存消息到数据库
     * @param prompt 用户输入
     * @param chatId 会话ID
     * @param chatType 会话类型
     * @return 流式响应
     */
    Flux<String> chat(String prompt, String chatId, String chatType);

    /**
     * 处理聊天请求并保存消息到数据库（SSE）
     * @param prompt 用户输入
     * @param chatId 会话ID
     * @param chat 会话类型
     * @return 流式响应
     */
    Flux<String> chatWithSSE(String prompt, String chatId, String chat);
}
