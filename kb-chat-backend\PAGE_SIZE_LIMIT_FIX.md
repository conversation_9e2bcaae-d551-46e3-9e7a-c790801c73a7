# 页面大小限制修复

## 问题描述

在获取未处理文件列表时，前端请求了 `page_size=1000`，但后端API限制每页大小必须在1-100之间，导致请求失败：

```
GET /dev-api/knowledge_base_files?kb_id=16&page_size=1000&status_filter=pending
响应: {"code":40000,"data":null,"message":"每页大小必须在1-100之间"}
```

## 问题根源

后端Controller中的参数验证：
```java
ThrowUtils.throwIf(pageSize < 1 || pageSize > 100, ErrorCode.PARAMS_ERROR, "每页大小必须在1-100之间");
```

前端请求时使用了超出限制的页面大小：
```javascript
page_size=1000  // 超出了100的限制
```

## 修复方案

### 实现分页获取所有未处理文件

```javascript
const getPendingFilesList = async () => {
  try {
    let allPendingFiles = [];
    let currentPage = 1;
    let hasMore = true;
    const pageSize = 100; // 使用最大允许的页面大小
    
    // 分页获取所有未处理文件
    while (hasMore) {
      const response = await fetch(
        `/dev-api/knowledge_base_files?kb_id=${fileProcessForm.value.kb_id}&include_files=true&page=${currentPage}&page_size=${pageSize}&status_filter=pending`
      );
      const result = await response.json();
      
      if (result.code === 0) {
        const files = result.data.files || [];
        allPendingFiles = allPendingFiles.concat(files);
        
        // 检查是否还有更多页面
        hasMore = result.data.pagination && result.data.pagination.has_more;
        currentPage++;
        
        console.log(`获取第${currentPage-1}页未处理文件: ${files.length}个，总计: ${allPendingFiles.length}个`);
      } else {
        console.error('获取未处理文件失败:', result.message);
        break;
      }
    }
    
    console.log(`总共获取到 ${allPendingFiles.length} 个未处理文件`);
    return allPendingFiles;
    
  } catch (error) {
    console.error('获取未处理文件失败:', error);
    return [];
  }
};
```

## 修复效果

### 修复前
```
请求: GET /dev-api/knowledge_base_files?page_size=1000&status_filter=pending
响应: {"code":40000,"message":"每页大小必须在1-100之间"}
结果: 获取文件失败，处理文档按钮无法工作
```

### 修复后
```
请求1: GET /dev-api/knowledge_base_files?page=1&page_size=100&status_filter=pending
响应1: {"code":0,"data":{"files":[...100个文件],"pagination":{"has_more":true}}}

请求2: GET /dev-api/knowledge_base_files?page=2&page_size=100&status_filter=pending  
响应2: {"code":0,"data":{"files":[...剩余文件],"pagination":{"has_more":false}}}

结果: 成功获取所有未处理文件，处理文档功能正常工作
```

## 技术特点

### 1. 自动分页处理
- 使用最大允许的页面大小（100）
- 自动检测是否有更多页面
- 循环获取直到获取所有文件

### 2. 错误处理
- 处理API调用失败的情况
- 提供详细的错误日志
- 优雅降级，返回空数组

### 3. 调试信息
- 输出每页获取的文件数量
- 显示总计获取的文件数
- 便于问题诊断和性能监控

### 4. 性能考虑
- 使用最大页面大小减少请求次数
- 及时终止循环避免无限请求
- 内存友好的数组拼接

## 使用场景

这个修复适用于以下场景：

### 场景1：少量未处理文件（≤100个）
- 只需要1次API调用
- 快速获取完整列表
- 用户体验流畅

### 场景2：大量未处理文件（>100个）
- 自动分页获取所有文件
- 控制台显示获取进度
- 确保不遗漏任何文件

### 场景3：网络错误或API异常
- 优雅处理错误情况
- 返回已获取的文件（如果有）
- 不会导致整个功能崩溃

## 控制台输出示例

```
获取第1页未处理文件: 100个，总计: 100个
获取第2页未处理文件: 50个，总计: 150个
总共获取到 150 个未处理文件
=== processDocuments called ===
pendingFilesList: [{name: "文件1.pdf", status: "pending"}, {name: "文件2.docx", status: "pending"}, ...]
```

## 后续优化建议

### 1. 缓存机制
- 缓存未处理文件列表，避免重复获取
- 设置合理的缓存过期时间
- 文件状态变化时清除缓存

### 2. 性能优化
- 考虑使用虚拟滚动处理大量文件
- 实现懒加载和按需获取
- 添加加载状态指示器

### 3. 用户体验
- 显示获取进度（"正在获取文件列表..."）
- 支持取消长时间的获取操作
- 提供重试机制

这个修复确保了无论知识库中有多少未处理文件，"处理文档"功能都能正常工作，同时遵守了后端API的参数限制。
