# 问答对保存重复检查重构

## 需求描述

用户要求：
1. 删除后端的`check_file_exists`接口
2. 删除前端对该接口的调用
3. 在`saveChat`方法中直接检查重复文档
4. 当发现重复文档时，返回相关信息给前端，而不是直接覆盖

## 修改内容

### 1. 后端修改

#### 删除Controller接口
**文件**: `QaController.java`
**修改**: 删除了`checkFileExists`方法和对应的`@GetMapping("/check_file_exists")`接口

```java
// 删除的代码
@Operation(summary = "检查文件是否存在")
@GetMapping("/check_file_exists")
public BaseResponse<Boolean> checkFileExists(
        @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
        @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
    ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "问答库名称不能为空");
    ThrowUtils.throwIf(fileName == null || fileName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "文件名称不能为空");
    boolean exists = qaService.checkFileExists(kbName, fileName);
    return ResultUtils.success(exists);
}
```

#### 删除Service接口方法
**文件**: `QaService.java`
**修改**: 删除了`checkFileExists`方法声明

```java
// 删除的代码
/**
 * 检查文件是否存在
 *
 * @param kbName 问答库名称
 * @param fileName 文件名称
 * @return 是否存在
 */
boolean checkFileExists(String kbName, String fileName);
```

#### 删除Service实现方法
**文件**: `QaServiceImpl.java`
**修改**: 删除了`checkFileExists`方法实现

```java
// 删除的代码
@Override
public boolean checkFileExists(String kbName, String fileName) {
    // 查找问答库
    QaBase qaBase = qaBaseMapper.selectOne(
        new LambdaQueryWrapper<QaBase>()
            .eq(QaBase::getName, kbName)
    );

    if (qaBase == null) {
        return false;
    }

    // 检查文件是否存在
    QaFile existingFile = qaFileMapper.selectOne(
        new LambdaQueryWrapper<QaFile>()
            .eq(QaFile::getQaBaseId, qaBase.getId())
            .eq(QaFile::getFileName, fileName)
    );

    return existingFile != null;
}
```

#### 修改saveChat方法
**文件**: `QaServiceImpl.java`
**修改**: 将重复文档检查逻辑集成到`saveChat`方法中

```java
// 修改前：删除旧记录并覆盖
if (existingFile != null) {
    // 删除旧的文件记录
    qaFileMapper.deleteById(existingFile.getId());
    
    // 删除旧的物理文件
    try {
        Path oldFilePath = Paths.get(existingFile.getFilePath());
        if (Files.exists(oldFilePath)) {
            Files.delete(oldFilePath);
        }
    } catch (IOException e) {
        log.warn("删除旧文件失败: {}", e.getMessage());
    }
}

// 修改后：返回重复信息
if (existingFile != null) {
    // 文件已存在，返回重复信息
    return "文件已存在：" + fileName + "。如需更新，请先删除原文件。";
}
```

### 2. 前端修改

#### 删除文件存在检查调用
**文件**: `AskBuild.vue`
**修改**: 删除了上传前的文件存在检查逻辑

```javascript
// 删除的代码
try {
  // 检查文件是否已存在
  const existingFiles = []
  for (const file of uploadForm.value.files) {
    const fileName = file.name
    const response = await fetch(
      `/dev-api/check_file_exists?kb_name=${encodeURIComponent(uploadForm.value.kbName)}&file_name=${encodeURIComponent(fileName)}`
    )
    const data = await response.json()

    if (data.code === 1 && data.data === true) {
      existingFiles.push(fileName)
    }
  }

  // 如果有文件已存在，询问用户是否覆盖
  if (existingFiles.length > 0) {
    try {
      await ElMessageBox.confirm(
        `以下文件已存在，是否覆盖？\n${existingFiles.join('\n')}`,
        '文件已存在',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消
    }
  }

// 修改后：简化为注释
try {
  // 删除文件存在检查，直接在saveChat中处理重复文档
```

#### 修改saveChat响应处理
**文件**: `ask/index.vue`
**修改**: 优化了saveChat的响应处理逻辑

```javascript
// 修改前
if (result.code === 1) {
  ElMessage.success("问答对已保存到知识库");
  saveDialogVisible.value = false;
} else {
  throw new Error(result.message || result.msg || "保存失败");
}

// 修改后
if (result.code === 0) {  // 修正状态码
  ElMessage.success("问答对已保存到知识库");
  saveDialogVisible.value = false;
} else {
  // 检查是否是重复文档的错误
  const errorMessage = result.message || result.msg || "保存失败";
  if (errorMessage.includes("文件已存在")) {
    ElMessage.warning(errorMessage);  // 使用warning而不是error
  } else {
    throw new Error(errorMessage);
  }
}
```

## 功能改进

### 1. 用户体验优化
- **之前**: 上传前检查文件是否存在，如果存在则询问是否覆盖
- **现在**: 直接尝试保存，如果重复则给出友好提示

### 2. 错误处理优化
- **之前**: 重复文档直接覆盖，用户可能丢失数据
- **现在**: 重复文档返回警告信息，保护现有数据

### 3. 接口简化
- **之前**: 需要两个接口：`check_file_exists` + `save_chat`
- **现在**: 只需要一个接口：`save_chat`

### 4. 状态码修正
- **修正**: 前端检查成功状态码从`code === 1`改为`code === 0`，与后端保持一致

## 测试场景

### 1. 正常保存
**操作**: 保存新的问答对到不存在的文件名
**预期**: 成功保存，显示"问答对已保存到知识库"

### 2. 重复文档处理
**操作**: 保存问答对到已存在的文件名
**预期**: 显示警告信息"文件已存在：xxx.json。如需更新，请先删除原文件。"

### 3. 其他错误处理
**操作**: 保存时发生其他错误（如网络错误、权限错误等）
**预期**: 显示具体的错误信息

## 优势

### 1. 数据安全
- 避免意外覆盖现有文档
- 用户需要主动删除才能更新

### 2. 接口简化
- 减少API调用次数
- 降低前后端交互复杂度

### 3. 用户体验
- 更清晰的错误提示
- 避免不必要的确认对话框

### 4. 代码维护
- 减少重复代码
- 集中处理重复检查逻辑

## 注意事项

### 1. 向后兼容
- 删除了`/check_file_exists`接口，如果有其他地方调用需要一并修改

### 2. 错误信息
- 重复文档的错误信息包含"文件已存在"关键字，前端据此判断

### 3. 状态码统一
- 确保前后端使用相同的成功状态码（0表示成功）

## 总结

这次重构简化了问答对保存的流程，提高了数据安全性，优化了用户体验。通过将重复检查逻辑集成到saveChat方法中，减少了API调用，同时提供了更友好的错误处理机制。

主要改进：
1. ✅ 删除了独立的文件存在检查接口
2. ✅ 在saveChat中集成重复检查逻辑
3. ✅ 优化了重复文档的处理方式
4. ✅ 修正了前端状态码检查逻辑
5. ✅ 提供了更友好的用户提示
