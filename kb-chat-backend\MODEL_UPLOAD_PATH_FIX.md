# 模型上传路径修复

## 问题描述

用户反馈：模型上传时的路径有问题，应该上传到`data/models/`目录下，而不是当前的硬编码路径。

## 问题分析

### 1. 当前问题
- `ModelStructureServiceImpl.uploadModelFolder()`方法使用硬编码路径`model_uploads`
- `getExportPath()`方法使用硬编码路径`model_exports`
- `exportModel()`方法使用硬编码路径`model_exports`
- 没有使用配置文件中定义的路径配置

### 2. 配置文件中的正确配置
```yaml
# application.yaml
app:
  model:
    upload-path: data/models
    export-path: data/exports
```

## 修复内容

### 1. ModelStructureServiceImpl.java 修复

#### 添加配置注入
```java
@Value("${app.model.upload-path:data/models}")
private String modelUploadPath;

@Value("${app.model.export-path:data/exports}")
private String modelExportPath;
```

#### 修复uploadModelFolder方法
```java
// 修复前
String targetDir = projectRoot + File.separator + "model_uploads" + File.separator + folderName + "_" + System.currentTimeMillis();

// 修复后
String targetDir = projectRoot + File.separator + modelUploadPath + File.separator + folderName + "_" + System.currentTimeMillis();
```

#### 修复getExportPath方法
```java
// 修复前
Path exportPath = Paths.get(projectRoot, "model_exports");

// 修复后
Path exportPath = Paths.get(projectRoot, modelExportPath);
```

#### 修复exportModel方法
```java
// 修复前
String exportDir = projectRoot + File.separator + "model_exports" + File.separator + "model_" + model.getVersion() + "_" + System.currentTimeMillis();

// 修复后
String exportDir = projectRoot + File.separator + modelExportPath + File.separator + "model_" + model.getVersion() + "_" + System.currentTimeMillis();
```

### 2. VersionController.java 验证

VersionController已经正确使用了配置：
```java
@Value("${app.model.upload-path:data/models}")
private String modelUploadPath;

@Value("${app.model.export-path:data/exports}")
private String modelExportPath;

@PostConstruct
public void init() {
    // 创建上传目录（基于项目根目录）
    Path uploadPath = Paths.get(projectRoot, modelUploadPath);
    // 创建导出目录（基于项目根目录）
    Path exportPath = Paths.get(projectRoot, modelExportPath);
}
```

## 修复后的目录结构

### 1. 模型上传目录
```
项目根目录/
├── data/
│   ├── models/
│   │   ├── model_folder_1_timestamp/
│   │   │   ├── config.json
│   │   │   ├── pytorch_model.bin
│   │   │   └── tokenizer.json
│   │   └── model_folder_2_timestamp/
│   │       └── ...
│   └── exports/
│       ├── model_v1.0_timestamp/
│       │   └── model_info.txt
│       └── model_v2.0_timestamp/
│           └── model_info.txt
```

### 2. 配置文件路径映射
- `app.model.upload-path: data/models` → 实际路径：`{项目根目录}/data/models/`
- `app.model.export-path: data/exports` → 实际路径：`{项目根目录}/data/exports/`

## 功能验证

### 1. 模型上传功能
- ✅ 上传的模型文件夹保存到`data/models/`目录
- ✅ 文件夹名称格式：`{原文件夹名}_{时间戳}`
- ✅ 保持原有的文件结构和安全检查

### 2. 导出路径获取
- ✅ 返回正确的导出路径：`{项目根目录}/data/exports/`
- ✅ 自动创建目录如果不存在

### 3. 模型导出功能
- ✅ 导出文件保存到`data/exports/`目录
- ✅ 导出文件夹名称格式：`model_{版本}_{时间戳}`

## 配置优势

### 1. 可配置性
- 可以通过配置文件修改上传和导出路径
- 支持不同环境使用不同路径配置

### 2. 统一管理
- 所有模型相关文件统一存储在`data/models/`
- 所有导出文件统一存储在`data/exports/`

### 3. 目录结构清晰
- 与知识库文件(`data/knowledge_bases/`)分离
- 与临时文件(`data/temp/`)分离
- 便于备份和管理

## 测试建议

### 1. 上传测试
1. 上传一个模型文件夹
2. 验证文件是否保存到`data/models/`目录
3. 检查文件结构是否完整

### 2. 导出测试
1. 调用获取导出路径接口
2. 验证返回的路径是否为`data/exports/`
3. 导出一个模型，检查文件是否保存到正确位置

### 3. 配置测试
1. 修改配置文件中的路径
2. 重启应用
3. 验证新路径是否生效

## 向后兼容

### 1. 默认值
- 如果配置文件中没有定义路径，使用默认值
- `upload-path`默认值：`data/models`
- `export-path`默认值：`data/exports`

### 2. 现有文件
- 修复不会影响已经上传的文件
- 新上传的文件将使用新路径
- 建议手动迁移旧文件到新路径（如果需要）

## 总结

这次修复解决了模型上传路径硬编码的问题：

1. ✅ 使用配置文件中定义的路径
2. ✅ 统一了模型文件的存储位置
3. ✅ 提高了系统的可配置性
4. ✅ 保持了原有功能的完整性
5. ✅ 改善了目录结构的组织

现在模型文件将正确上传到`data/models/`目录，导出文件将保存到`data/exports/`目录，符合系统的整体文件组织结构。
