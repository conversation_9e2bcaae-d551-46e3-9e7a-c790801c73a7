package com.ai.aichat.service.impl;

import com.ai.aichat.config.FileStorageConfig;
import com.ai.aichat.mapper.KnowledgeBaseFileMapper;
import com.ai.aichat.model.entity.KnowledgeBase;
import com.ai.aichat.model.entity.KnowledgeBaseFile;
import com.ai.aichat.model.vo.response.KnowledgeBaseFileVo;
import com.ai.aichat.service.KnowledgeBaseFileService;
import com.ai.aichat.service.KnowledgeBaseService;
import com.ai.aichat.service.MilvusService;
import com.ai.aichat.util.FileUtils;
import com.ai.aichat.util.TikaUtil;
import com.ai.aichat.util.TikaVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识库文件服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KnowledgeBaseFileServiceImpl extends ServiceImpl<KnowledgeBaseFileMapper, KnowledgeBaseFile> implements KnowledgeBaseFileService {

    private final FileStorageConfig fileStorageConfig;
    private final KnowledgeBaseService knowledgeBaseService;
    private final MilvusService milvusService;
    private final OpenAiEmbeddingModel openAiEmbeddingModel;
    private final TikaUtil tikaUtil;

    @Override
    @Transactional
    public Map<String, Object> uploadFile(MultipartFile file, Long kbId, String kbName) {
        try {
            // 验证参数
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("文件不能为空");
            }
            
            if (kbId == null || kbName == null || kbName.trim().isEmpty()) {
                throw new RuntimeException("知识库或名称不能为空");
            }

            // 验证知识库是否存
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityById(kbId);
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库不存在: " + kbName);
            }

            String originalFileName = file.getOriginalFilename();
            
            // 验证文件名
            if (!FileUtils.isValidFileName(originalFileName)) {
                throw new RuntimeException("文件名无效");
            }

            // 验证文件类型
            if (!FileUtils.isAllowedFileType(originalFileName)) {
                throw new RuntimeException("不支持的文件类型");
            }

            // 验证文件大小
            if (file.getSize() > fileStorageConfig.getMaxFileSize()) {
                throw new RuntimeException("文件大小超过限制: " + FileUtils.formatFileSize(fileStorageConfig.getMaxFileSize()));
            }

            // 计算文件MD5
            String fileMd5 = FileUtils.calculateMD5(file);
            
            // 检查文件是否已存在
            if (fileExists(kbName, originalFileName, fileMd5)) {
                throw new RuntimeException("文件已存在");
            }

            // 生成唯一文件名
            String uniqueFileName = FileUtils.generateUniqueFileName(originalFileName);
            
            // 构建文件路径
            String docsPath = fileStorageConfig.getKnowledgeBaseDocsPath(kbName);
            String filePath = docsPath + File.separator + uniqueFileName;

            log.info("文件保存路径: {}", filePath);
            log.info("文档目录路径: {}", docsPath);

            // 保存文件到磁盘
            FileUtils.saveFile(file, filePath);

            // 保存文件信息到数据库
            KnowledgeBaseFile fileEntity = new KnowledgeBaseFile();
            fileEntity.setKbId(kbId);
            fileEntity.setFileName(uniqueFileName);
            fileEntity.setOriginalName(originalFileName);
            // 存储绝对路径（kb_name/文件名）
            fileEntity.setFilePath(fileStorageConfig.getKnowledgeBaseDocsPath(kbName) + File.separator + uniqueFileName);
            fileEntity.setFileSize(file.getSize());
            fileEntity.setFileType(FileUtils.getFileExtension(originalFileName));
            fileEntity.setFileMd5(fileMd5);
            fileEntity.setProcessStatus("pending");
            fileEntity.setProcessProgress(0);
            fileEntity.setUserId(1L); // 默认用户ID
            // isDelete、createTime、updateTime字段会通过自动填充设置

            save(fileEntity);

            log.info("文件上传成功: {} -> {}", originalFileName, uniqueFileName);

            return Map.of(
                "success", true,
                "file_id", fileEntity.getId(),
                "file_name", uniqueFileName,
                "original_name", originalFileName,
                "file_size", file.getSize(),
                "message", "文件上传成功"
            );

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Boolean removeFile(Long id, String kbName, String fileName) {
        try {
            // 先获取知识库ID
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库不存在: " + kbName);
            }

            // 判断知识库id和知识库名称是否一致
            if (!knowledgeBase.getId().equals(id)) {
                throw new RuntimeException("知识库ID和名称不匹配: " + id + " " + kbName);
            }

            // 查找文件记录（根据唯一文件名）
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", id)
                    .eq("original_name", fileName)
                    .eq("is_delete", 0);

            KnowledgeBaseFile fileEntity = getOne(queryWrapper);

            if (fileEntity == null) {
                throw new RuntimeException("文件不存在");
            }

            // 删除物理文件
            String md5FileName = fileEntity.getFileName();
            String filePath = fileStorageConfig.getKnowledgeBaseDocsPath(kbName) + File.separator + md5FileName;
            if (FileUtils.fileExists(filePath)) {
                FileUtils.deleteFile(fileEntity.getFilePath());
            }

            // 软删除数据库记录 - 使用MyBatis-Plus的逻辑删除功能
            boolean deleteResult = removeById(fileEntity.getId());

            if (!deleteResult) {
                throw new RuntimeException("数据库删除失败");
            }

            // 删除向量数据库中的数据
            String collectionName = "kb_" + id;
            milvusService.delete(collectionName, fileName);

            log.info("文件删除成功: {}/{}", kbName, fileName);
            return true;

        } catch (Exception e) {
            log.error("文件删除失败: {}/{}", kbName, fileName, e);
            throw new RuntimeException("文件删除失败: " + e.getMessage());
        }
    }

    @Override
    public List<KnowledgeBaseFileVo> getKnowledgeBaseFiles(String kbName) {
        try {
            // 暂时使用kb_name字段查询（等待数据库迁移）
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_name", kbName)
                       .eq("is_delete", 0)
                       .orderByDesc("create_time");

            List<KnowledgeBaseFile> files = list(queryWrapper);

            return files.stream()
                    .map(this::convertToVo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取知识库文件列表失败: {}", kbName, e);
            throw new RuntimeException("获取知识库文件列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> processDocuments(Long kbId, String kbName, List<String> fileNames) {
        try {
            // 获取知识库信息
            KnowledgeBase knowledgeBase = null;
            if (kbId != null) {
                knowledgeBase = knowledgeBaseService.getById(kbId);
            }
            if (knowledgeBase == null && kbName != null) {
                knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            }
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库不存在: " + (kbId != null ? kbId : kbName));
            }
            Long realKbId = knowledgeBase.getId();

            // collectionName 用 kbId，描述用 kbName
            String collectionName = "kb_" + realKbId;

            // 遍历每个文件，读取内容，切分、向量化、插入 Milvus
            for (String fileName : fileNames) {
                QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("kb_id", realKbId)
                           .eq("original_name", fileName)
                           .eq("is_delete", 0);
                KnowledgeBaseFile fileEntity = getOne(queryWrapper);
                if (fileEntity != null) {
                    fileEntity.setProcessStatus("processing");
                    fileEntity.setProcessProgress(0);
                    updateById(fileEntity);
                    
                    // 获取文件内容
                    TikaVo tikaVo = tikaUtil.extractText(new java.io.File(fileEntity.getFilePath()));
                    List<String> chunkList = tikaVo != null ? tikaVo.getText() : List.of();
                    // 向量化
                    List<float[]> vectors = chunkList.stream().map(openAiEmbeddingModel::embed).toList();
                    // 插入 Milvus
                    milvusService.batchInsert(collectionName, vectors, chunkList, Collections.nCopies(chunkList.size(), "{}"), Collections.nCopies(chunkList.size(), fileName));

                    // 更新状态为已完成
                    fileEntity.setProcessStatus("completed");
                    fileEntity.setProcessProgress(100);
                    updateById(fileEntity);
                }
            }

            return Map.of(
                "success", true,
                "message", "文档处理已开始",
                "processed_files", fileNames.size(),
                "collection", collectionName
            );

        } catch (Exception e) {
            log.error("文档处理失败: {}", kbName, e);
            throw new RuntimeException("文档处理失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessProgress(String kbName) {
        try {
            // 先获取知识库ID
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库不存在: " + kbName);
            }

            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", knowledgeBase.getId())
                       .eq("is_delete", 0);

            List<KnowledgeBaseFile> files = list(queryWrapper);
            
            long totalFiles = files.size();
            long completedFiles = files.stream()
                    .filter(f -> "completed".equals(f.getProcessStatus()))
                    .count();
            long processingFiles = files.stream()
                    .filter(f -> "processing".equals(f.getProcessStatus()))
                    .count();
            long failedFiles = files.stream()
                    .filter(f -> "failed".equals(f.getProcessStatus()))
                    .count();

            int overallProgress = totalFiles > 0 ? (int) ((completedFiles * 100) / totalFiles) : 100;

            return Map.of(
                "total_files", totalFiles,
                "completed_files", completedFiles,
                "processing_files", processingFiles,
                "failed_files", failedFiles,
                "overall_progress", overallProgress,
                "status", processingFiles > 0 ? "processing" : "completed"
            );

        } catch (Exception e) {
            log.error("获取处理进度失败: {}", kbName, e);
            throw new RuntimeException("获取处理进度失败: " + e.getMessage());
        }
    }

    @Override
    public KnowledgeBaseFileVo getFileInfo(String kbName, String fileName) {
        try {
            // 先获取知识库ID
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库不存在: " + kbName);
            }

            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", knowledgeBase.getId())
                       .eq("file_name", fileName)
                       .eq("is_delete", 0);

            KnowledgeBaseFile fileEntity = getOne(queryWrapper);
            
            if (fileEntity == null) {
                throw new RuntimeException("文件不存在");
            }

            return convertToVo(fileEntity);

        } catch (Exception e) {
            log.error("获取文件信息失败: {}/{}", kbName, fileName, e);
            throw new RuntimeException("获取文件信息失败: " + e.getMessage());
        }
    }

    @Override
    public void updateProcessStatus(Long fileId, String status, Integer progress, String errorMessage) {
        try {
            KnowledgeBaseFile fileEntity = getById(fileId);
            if (fileEntity != null) {
                fileEntity.setProcessStatus(status);
                fileEntity.setProcessProgress(progress);
                fileEntity.setErrorMessage(errorMessage);
                updateById(fileEntity);
            }
        } catch (Exception e) {
            log.error("更新文件处理状态失败: {}", fileId, e);
        }
    }

    @Override
    public Boolean fileExists(String kbName, String fileName, String fileMd5) {
        try {
            // 先获取知识库ID
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                return false; // 知识库不存在，文件肯定不存在
            }

            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", knowledgeBase.getId())
                       .eq("is_delete", 0);

            if (fileName != null) {
                queryWrapper.eq("original_name", fileName);
            }

            if (fileMd5 != null) {
                queryWrapper.eq("file_md5", fileMd5);
            }

            return getOne(queryWrapper) != null;
        } catch (Exception e) {
            log.error("检查文件是否存在失败: {}", kbName, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Integer removeAllFilesByKnowledgeBase(String kbName) {
        try {
            log.info("开始删除知识库 {} 下的所有文件", kbName);

            // 先获取知识库ID
            KnowledgeBase knowledgeBase = knowledgeBaseService.getKnowledgeBaseEntityByName(kbName);
            if (knowledgeBase == null) {
                log.warn("知识库不存在: {}", kbName);
                return 0;
            }

            // 查询该知识库下的所有文件
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", knowledgeBase.getId())
                       .eq("is_delete", 0);

            List<KnowledgeBaseFile> files = list(queryWrapper);

            if (files.isEmpty()) {
                log.info("知识库 {} 下没有文件需要删除", kbName);
                return 0;
            }

            int deletedCount = 0;
            for (KnowledgeBaseFile file : files) {
                try {
                    // 删除物理文件
                    if (FileUtils.fileExists(file.getFilePath())) {
                        FileUtils.deleteFile(file.getFilePath());
                    }

                    // 逻辑删除数据库记录
                    boolean deleted = removeById(file.getId());
                    if (deleted) {
                        deletedCount++;
                        log.debug("删除文件成功: {} ({})", file.getOriginalName(), file.getFileName());
                    }
                } catch (Exception e) {
                    log.warn("删除文件失败: {} - {}", file.getOriginalName(), e.getMessage());
                    // 继续删除其他文件
                }
            }

            log.info("知识库 {} 文件删除完成，总计: {}, 成功: {}", kbName, files.size(), deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("批量删除知识库文件失败: {}", kbName, e);
            throw new RuntimeException("批量删除知识库文件失败: " + e.getMessage());
        }
    }

    /**
     * 转换实体为VO
     */
    private KnowledgeBaseFileVo convertToVo(KnowledgeBaseFile fileEntity) {
        KnowledgeBaseFileVo vo = new KnowledgeBaseFileVo();
        vo.setId(fileEntity.getId());

        // 通过kb_id获取知识库名称
        try {
            KnowledgeBase knowledgeBase = knowledgeBaseService.getById(fileEntity.getKbId());
            if (knowledgeBase != null) {
                vo.setKb_name(knowledgeBase.getName());
            }
        } catch (Exception e) {
            log.warn("获取知识库名称失败: kb_id={}", fileEntity.getKbId(), e);
            vo.setKb_name("未知知识库");
        }

        // 前端显示用原始文件名
        vo.setName(fileEntity.getOriginalName());
        // 后端标识用唯一文件名
        vo.setFile_name(fileEntity.getFileName());

        vo.setSize(fileEntity.getFileSize());
        vo.setFile_size_formatted(FileUtils.formatFileSize(fileEntity.getFileSize()));
        vo.setFile_type(fileEntity.getFileType());
        vo.setStatus(fileEntity.getProcessStatus());
        vo.setProgress(fileEntity.getProcessProgress());
        vo.setError_message(fileEntity.getErrorMessage());

        // 格式化时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (fileEntity.getCreateTime() != null) {
            vo.setUpload_time(sdf.format(fileEntity.getCreateTime()));
        }
        if (fileEntity.getUpdateTime() != null) {
            vo.setUpdated_at(sdf.format(fileEntity.getUpdateTime()));
        }

        return vo;
    }

    // ========== 基于kb_id的新方法实现 ==========


    @Override
    @Transactional
    public Boolean removeFileByKbId(Long kbId, String fileName) {
        try {
            log.info("删除文件请求: kb_id={}, fileName={}", kbId, fileName);

            // 尝试按原始文件名查找
            QueryWrapper<KnowledgeBaseFile> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("kb_id", kbId)
                        .eq("original_name", fileName)
                        .eq("is_delete", 0);
            KnowledgeBaseFile fileEntity1 = getOne(queryWrapper1);

            // 如果按原始名称找不到，尝试按文件名查找
            KnowledgeBaseFile fileEntity = fileEntity1;
            if (fileEntity == null) {
                QueryWrapper<KnowledgeBaseFile> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2.eq("kb_id", kbId)
                            .eq("file_name", fileName)
                            .eq("is_delete", 0);
                fileEntity = getOne(queryWrapper2);
            }

            if (fileEntity == null) {
                throw new RuntimeException("文件不存在: " + fileName);
            }

            // 删除物理文件
            if (FileUtils.fileExists(fileEntity.getFilePath())) {
                FileUtils.deleteFile(fileEntity.getFilePath());
            }

            // 逻辑删除数据库记录
            boolean deleteResult = removeById(fileEntity.getId());

            if (!deleteResult) {
                throw new RuntimeException("数据库删除失败");
            }

            log.info("文件删除成功: kb_id={}, fileName={}", kbId, fileName);
            return true;

        } catch (Exception e) {
            log.error("文件删除失败: kb_id={}, fileName={}", kbId, fileName, e);
            throw new RuntimeException("文件删除失败: " + e.getMessage());
        }
    }

    @Override
    public List<KnowledgeBaseFileVo> getKnowledgeBaseFilesByKbId(Long kbId) {
        try {
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", kbId)
                       .eq("is_delete", 0)
                       .orderByDesc("create_time");

            List<KnowledgeBaseFile> files = list(queryWrapper);

            return files.stream()
                    .map(this::convertToVo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取知识库文件列表失败: kb_id={}", kbId, e);
            throw new RuntimeException("获取知识库文件列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Integer removeAllFilesByKbId(Long kbId) {
        try {
            log.info("开始删除知识库下的所有文件: kb_id={}", kbId);

            // 查询该知识库下的所有文件
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", kbId)
                       .eq("is_delete", 0);

            List<KnowledgeBaseFile> files = list(queryWrapper);

            if (files.isEmpty()) {
                log.info("知识库下没有文件需要删除: kb_id={}", kbId);
                return 0;
            }

            int deletedCount = 0;
            for (KnowledgeBaseFile file : files) {
                try {
                    // 删除物理文件
                    if (FileUtils.fileExists(file.getFilePath())) {
                        FileUtils.deleteFile(file.getFilePath());
                    }

                    // 逻辑删除数据库记录
                    boolean deleted = removeById(file.getId());
                    if (deleted) {
                        deletedCount++;
                        log.debug("删除文件成功: {} ({})", file.getOriginalName(), file.getFileName());
                    }
                } catch (Exception e) {
                    log.warn("删除文件失败: {} - {}", file.getOriginalName(), e.getMessage());
                    // 继续删除其他文件
                }
            }

            log.info("知识库文件删除完成: kb_id={}, 总计: {}, 成功: {}", kbId, files.size(), deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("批量删除知识库文件失败: kb_id={}", kbId, e);
            throw new RuntimeException("批量删除知识库文件失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getKnowledgeBaseFilesPaged(Long kbId, String kbName, Integer page, Integer pageSize) {
        try {
            log.info("获取知识库文件列表: kb_id={}, kb_name={}, page: {}, pageSize: {}", kbId, kbName, page, pageSize);

            List<KnowledgeBaseFileVo> allFiles;
            if (kbId != null) {
                allFiles = getKnowledgeBaseFilesByKbId(kbId);
            } else {
                allFiles = getKnowledgeBaseFiles(kbName);
            }

            // 简单的内存分页（生产环境建议在数据库层面分页）
            int total = allFiles.size();
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<KnowledgeBaseFileVo> pagedFiles = startIndex < total ?
                allFiles.subList(startIndex, endIndex) : List.of();

            // 构建分页信息对象
            Map<String, Object> pagination = Map.of(
                "page", page,
                "page_size", pageSize,
                "total_count", total,
                "total_pages", (int) Math.ceil((double) total / pageSize),
                "has_more", page < (int) Math.ceil((double) total / pageSize)
            );

            return Map.of(
                "files", pagedFiles,
                "pagination", pagination
            );

        } catch (Exception e) {
            log.error("获取知识库文件列表失败: kbId={}, kbName={}", kbId, kbName, e);
            throw new RuntimeException("获取知识库文件列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> commonUpload(MultipartFile file) {
        try {
            log.info("通用文件上传: {}", file.getOriginalFilename());

            // 这里可以实现通用的文件上传逻辑
            // 目前返回模拟结果，与前端FileUpload组件兼容
            String fileName = file.getOriginalFilename();
            long fileSize = file.getSize();

            // 可以在这里添加实际的文件保存逻辑
            // 例如保存到本地文件系统或云存储

            return Map.of(
                "fileName", fileName,
                "url", "/files/" + fileName,
                "size", fileSize,
                "uploadTime", System.currentTimeMillis()
            );

        } catch (Exception e) {
            log.error("通用文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public Integer getFileCountByKbId(Long kbId) {
        try {
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", kbId)
                       .eq("is_delete", 0);

            return Math.toIntExact(count(queryWrapper));

        } catch (Exception e) {
            log.error("获取知识库文件数量失败，kb_id: {}", kbId, e);
            return 0;
        }
    }

    /**
     * 检查文件是否已存在（使用kb_id）
     */
    private Boolean fileExistsByKbId(Long kbId, String fileName, String fileMd5) {
        try {
            QueryWrapper<KnowledgeBaseFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kb_id", kbId)
                       .eq("is_delete", 0);

            if (fileName != null) {
                queryWrapper.eq("original_name", fileName);
            }

            if (fileMd5 != null) {
                queryWrapper.eq("file_md5", fileMd5);
            }

            return getOne(queryWrapper) != null;
        } catch (Exception e) {
            log.error("检查文件是否存在失败: kb_id={}", kbId, e);
            return false;
        }
    }
}
