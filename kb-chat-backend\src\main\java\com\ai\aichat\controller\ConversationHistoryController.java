package com.ai.aichat.controller;

import com.ai.aichat.model.dto.request.UpdateConversationTitleDto;
import com.ai.aichat.model.vo.response.ChatHistoryListVo;
import com.ai.aichat.model.vo.response.ConversationListVo;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.ai.aichat.service.ConversationHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 会话历史控制器
 */
@Slf4j
@Tag(name = "会话历史管理")
@RequiredArgsConstructor
@RestController
@Validated
public class ConversationHistoryController {

    private final ConversationHistoryService conversationHistoryService;

    @Operation(summary = "获取历史会话列表")
    @GetMapping("/get_conversations")
    public StreamResponseVo getConversations(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            
            @Parameter(description = "每页数量", example = "20")
            @RequestParam(defaultValue = "20") Integer page_size,
            
            @Parameter(description = "聊天类型", example = "chat")
            @RequestParam(required = false) String chat_type,
            
            @Parameter(description = "日期范围")
            @RequestParam(required = false) String date_range) {
        
        try {
            log.info("获取会话列表: page={}, pageSize={}, chatType={}, dateRange={}", 
                    page, page_size, chat_type, date_range);
            
            ConversationListVo result = conversationHistoryService.getConversations(
                    page, page_size, chat_type, date_range);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(result)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取会话列表失败", e);
            return StreamResponseVo.error("获取会话列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取指定会话的聊天历史")
    @GetMapping("/chat_history")
    public StreamResponseVo getChatHistory(
            @Parameter(description = "会话ID", required = true)
            @RequestParam @NotBlank(message = "会话ID不能为空") String session_id) {
        
        try {
            log.info("获取聊天历史: sessionId={}", session_id);
            
            ChatHistoryListVo result = conversationHistoryService.getChatHistory(session_id);
            
            return StreamResponseVo.builder()
                    .code(1)
                    .msg("success")
                    .data(result)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取聊天历史失败: sessionId={}", session_id, e);
            return StreamResponseVo.error("获取聊天历史失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除指定会话")
    @DeleteMapping("/delete_conversation/{sessionId}")
    public StreamResponseVo deleteConversation(
            @Parameter(description = "会话ID", required = true)
            @PathVariable @NotBlank(message = "会话ID不能为空") String sessionId) {
        
        try {
            log.info("删除会话: sessionId={}", sessionId);
            
            Boolean result = conversationHistoryService.deleteConversation(sessionId);
            
            if (result) {
                return StreamResponseVo.builder()
                        .code(1)
                        .msg("删除成功")
                        .data(null)
                        .build();
            } else {
                return StreamResponseVo.error("删除失败，会话不存在或已被删除");
            }
                    
        } catch (Exception e) {
            log.error("删除会话失败: sessionId={}", sessionId, e);
            return StreamResponseVo.error("删除会话失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新会话标题")
    @PostMapping("/update_conversation_title")
    public StreamResponseVo updateConversationTitle(
            @Parameter(description = "更新会话标题请求", required = true)
            @RequestBody @Valid UpdateConversationTitleDto request) {
        
        try {
            log.info("更新会话标题: sessionId={}, title={}", 
                    request.getSession_id(), request.getTitle());
            
            Boolean result = conversationHistoryService.updateConversationTitle(
                    request.getSession_id(), request.getTitle());
            
            if (result) {
                return StreamResponseVo.builder()
                        .code(1)
                        .msg("更新成功")
                        .data(null)
                        .build();
            } else {
                return StreamResponseVo.error("更新失败，会话不存在");
            }
                    
        } catch (Exception e) {
            log.error("更新会话标题失败: sessionId={}, title={}", 
                    request.getSession_id(), request.getTitle(), e);
            return StreamResponseVo.error("更新会话标题失败: " + e.getMessage());
        }
    }
}
